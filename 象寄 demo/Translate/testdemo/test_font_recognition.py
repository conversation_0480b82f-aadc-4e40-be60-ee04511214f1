"""
字体识别测试程序
从主程序中抽取字体识别算法进行独立测试

核心功能：
1. OCR文字识别
2. 字体样本生成
3. SSIM相似度计算
4. 字体匹配算法
5. 日文支持检测
6. 可视化对比

作者：测试开发
"""

import os
import cv2
import numpy as np
import json
from typing import List, Tuple, Dict, Any
from PIL import Image, ImageDraw, ImageFont
from paddleocr import PaddleOCR
from skimage.metrics import structural_similarity as ssim
import re
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import matplotlib.font_manager as fm

# 配置matplotlib支持中文字体
def setup_chinese_font():
    """设置中文字体支持"""
    # 尝试使用系统中文字体
    chinese_fonts = [
        'PingFang SC',  # macOS 系统字体
        'Hiragino Sans GB',  # macOS 中文字体
        'Microsoft YaHei',  # Windows 中文字体
        'SimHei',  # Windows 黑体
        'Arial Unicode MS',  # 通用Unicode字体
        'DejaVu Sans'  # 备用字体
    ]
    
    # 检查可用字体
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    
    for font in chinese_fonts:
        if font in available_fonts:
            plt.rcParams['font.sans-serif'] = [font]
            plt.rcParams['axes.unicode_minus'] = False
            print(f"✅ 使用中文字体: {font}")
            return
    
    # 如果没有找到合适的字体，使用默认配置
    plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    print("⚠️  未找到合适的中文字体，可能出现显示问题")

# 初始化中文字体
setup_chinese_font()

class FontRecognitionTester:
    """字体识别测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.ocr = None
        self.debug_dir = "font_debug"
        self.ensure_debug_dir()
        
        # 字体识别参数
        self.sample_size = (64, 64)
        self.font_size = 32
        
        # 字体映射（从主程序复制）
        self.font_mapping = self._load_font_mapping()
        
        # 中文判断正则
        self._chinese_pattern = re.compile(r'[\u4e00-\u9fff]+')
        
        print("🎨 字体识别测试器初始化完成")
        print(f"📁 加载了 {len(self.font_mapping)} 个字体")
    
    def ensure_debug_dir(self):
        """确保调试目录存在"""
        os.makedirs(self.debug_dir, exist_ok=True)
    
    def _load_font_mapping(self) -> Dict[str, str]:
        """加载字体映射"""
        font_mapping = {}
        fonts_dir = "../fonts"
        
        if not os.path.exists(fonts_dir):
            print("❌ 字体目录不存在")
            return font_mapping
        
        # 扫描字体目录
        for font_folder in os.listdir(fonts_dir):
            font_path = os.path.join(fonts_dir, font_folder)
            if not os.path.isdir(font_path):
                continue
            
            # 查找字体文件
            for file in os.listdir(font_path):
                if file.endswith(('.ttf', '.otf', '.ttc')):
                    font_mapping[font_folder] = os.path.join(font_path, file)
                    break
        
        return font_mapping
    
    def _get_ocr_instance(self):
        """获取OCR实例"""
        if self.ocr is None:
            print("🔄 初始化PaddleOCR...")
            try:
                self.ocr = PaddleOCR(
                    use_doc_orientation_classify=False,
                    use_doc_unwarping=False,
                    use_textline_orientation=False,
                    lang='ch'
                )
                print("✅ PaddleOCR初始化完成")
            except Exception as e:
                print(f"❌ PaddleOCR初始化失败: {e}")
                return None
        return self.ocr
    
    def is_chinese_text(self, text: str) -> bool:
        """判断文本是否包含中文字符"""
        return bool(self._chinese_pattern.search(text))
    
    def test_font_recognition(self, image_path: str):
        """测试字体识别"""
        print(f"\n🎯 开始字体识别测试: {image_path}")
        
        # 1. OCR识别
        ocr = self._get_ocr_instance()
        if not ocr:
            return
        
        print("📖 进行OCR识别...")
        result = ocr.predict(input=image_path)
        
        if not result:
            print("❌ 未检测到文字")
            return
        
        # 2. 加载图像
        image = cv2.imread(image_path)
        if image is None:
            print(f"❌ 无法加载图像: {image_path}")
            return
        
        # 3. 解析OCR结果
        chinese_regions = self._parse_ocr_result(result, image)
        
        if not chinese_regions:
            print("❌ 未检测到中文区域")
            return
        
        print(f"📝 检测到 {len(chinese_regions)} 个中文区域")
        
        # 4. 进行字体识别
        font_results = []
        for region in chinese_regions:
            print(f"\n🔍 分析文字: '{region['text']}'")
            
            # 提取文字区域
            text_region = self._extract_text_region(image, region['bbox'])
            if text_region is None:
                continue
            
            # 字体匹配
            matched_font, confidence, similarity_scores = self._match_font_with_details(
                text_region, region['text']
            )
            
            # 日文支持检测
            font_path = self.font_mapping.get(matched_font, "")
            supports_japanese = self._test_japanese_support(font_path) if font_path else False
            
            result_data = {
                'text': region['text'],
                'bbox': region['bbox'],
                'matched_font': matched_font,
                'font_path': font_path,
                'confidence': confidence,
                'supports_japanese': supports_japanese,
                'similarity_scores': similarity_scores,
                'original_region': text_region
            }
            
            font_results.append(result_data)
            
            print(f"   🎯 最佳匹配: {matched_font}")
            print(f"   📊 相似度: {confidence:.3f}")
            print(f"   🌏 日文支持: {'是' if supports_japanese else '否'}")
            
            # 显示前3名
            print("   🏆 排行榜:")
            for i, (font_name, score) in enumerate(similarity_scores[:3]):
                print(f"      {i+1}. {font_name}: {score:.3f}")
        
        # 5. 生成可视化结果
        self._generate_visualization(image_path, image, font_results)
        
        # 6. 保存详细结果
        self._save_detailed_results(image_path, font_results)
        
        print(f"\n✅ 字体识别测试完成！结果保存在 {self.debug_dir}/ 目录下")
    
    def _parse_ocr_result(self, raw_result, image: np.ndarray) -> List[Dict]:
        """解析OCR结果"""
        chinese_regions = []
        
        for res in raw_result:
            if 'rec_texts' in res and 'dt_polys' in res:
                dt_polys = res['dt_polys']
                rec_texts = res['rec_texts']
                rec_scores = res.get('rec_scores', [1.0] * len(rec_texts))
                
                for poly, text, score in zip(dt_polys, rec_texts, rec_scores):
                    if score < 0.5:  # 置信度阈值
                        continue
                    
                    if not self.is_chinese_text(text):
                        continue
                    
                    # 计算边界框
                    x_coords = [p[0] for p in poly]
                    y_coords = [p[1] for p in poly]
                    x, y = int(min(x_coords)), int(min(y_coords))
                    w, h = int(max(x_coords) - x), int(max(y_coords) - y)
                    
                    chinese_regions.append({
                        'text': text,
                        'bbox': (x, y, w, h),
                        'poly': poly,
                        'score': score
                    })
        
        return chinese_regions
    
    def _extract_text_region(self, image: np.ndarray, bbox: Tuple[int, int, int, int]) -> np.ndarray:
        """提取文字区域"""
        try:
            x, y, w, h = bbox
            x = max(0, x)
            y = max(0, y)
            w = min(w, image.shape[1] - x)
            h = min(h, image.shape[0] - y)
            
            if w <= 0 or h <= 0:
                return None
            
            return image[y:y+h, x:x+w]
        except Exception as e:
            print(f"   ❌ 提取文字区域失败: {e}")
            return None
    
    def _match_font_with_details(self, text_region: np.ndarray, text: str) -> Tuple[str, float, List[Tuple[str, float]]]:
        """字体匹配（带详细信息）"""
        if len(self.font_mapping) == 0:
            return "默认字体", 0.0, []
        
        # 预处理文字区域
        processed_region = self._preprocess_text_region(text_region)
        
        similarity_scores = []
        
        # 遍历所有字体进行匹配
        for font_name, font_path in self.font_mapping.items():
            try:
                # 生成字体样本
                font_sample = self._generate_font_sample(text, font_path)
                if font_sample is None:
                    continue
                
                # 计算相似度
                similarity = self._calculate_similarity(processed_region, font_sample)
                similarity_scores.append((font_name, similarity))
                
            except Exception as e:
                print(f"   ⚠️ 字体匹配错误 {font_name}: {e}")
                continue
        
        # 按相似度排序
        similarity_scores.sort(key=lambda x: x[1], reverse=True)
        
        if similarity_scores:
            best_font, best_similarity = similarity_scores[0]
            return best_font, best_similarity, similarity_scores
        else:
            return "默认字体", 0.0, []
    
    def _preprocess_text_region(self, region: np.ndarray) -> np.ndarray:
        """预处理文字区域"""
        if len(region.shape) == 3:
            gray = cv2.cvtColor(region, cv2.COLOR_BGR2GRAY)
        else:
            gray = region.copy()
        
        # 调整大小
        resized = cv2.resize(gray, self.sample_size)
        
        # 二值化
        _, binary = cv2.threshold(resized, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        return binary
    
    def _generate_font_sample(self, text: str, font_path: str) -> np.ndarray:
        """生成字体样本"""
        try:
            # 创建PIL图像
            img = Image.new('L', self.sample_size, 255)
            draw = ImageDraw.Draw(img)
            
            # 加载字体
            font = ImageFont.truetype(font_path, self.font_size)
            
            # 计算文字位置（居中）
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            
            x = (self.sample_size[0] - text_width) // 2
            y = (self.sample_size[1] - text_height) // 2
            
            # 绘制文字
            draw.text((x, y), text, font=font, fill=0)
            
            # 转换为numpy数组
            sample = np.array(img)
            
            return sample
            
        except Exception as e:
            print(f"   ❌ 生成字体样本失败 {font_path}: {e}")
            return None
    
    def _calculate_similarity(self, region1: np.ndarray, region2: np.ndarray) -> float:
        """计算两个图像的相似度"""
        try:
            # 确保图像大小一致
            if region1.shape != region2.shape:
                region2 = cv2.resize(region2, (region1.shape[1], region1.shape[0]))
            
            # 使用SSIM计算结构相似性
            similarity = ssim(region1, region2)
            return max(0.0, similarity)  # 确保非负
            
        except Exception as e:
            print(f"   ❌ 相似度计算失败: {e}")
            return 0.0
    
    def _test_japanese_support(self, font_path: str) -> bool:
        """测试字体是否支持日文"""
        if not font_path:
            return False
            
        try:
            # 测试日文字符
            test_chars = ['あ', 'か', 'さ', 'た', 'な']  # 平假名
            
            font = ImageFont.truetype(font_path, 24)
            
            for char in test_chars:
                try:
                    # 尝试获取字符的边界框
                    img = Image.new('RGB', (50, 50), 'white')
                    draw = ImageDraw.Draw(img)
                    bbox = draw.textbbox((0, 0), char, font=font)
                    
                    # 如果边界框有效，说明字体支持该字符
                    if bbox[2] > bbox[0] and bbox[3] > bbox[1]:
                        return True
                        
                except Exception:
                    continue
            
            return False
            
        except Exception as e:
            print(f"   ❌ 日文支持测试失败 {font_path}: {e}")
            return False
    
    def _generate_visualization(self, image_path: str, image: np.ndarray, font_results: List[Dict]):
        """生成可视化结果"""
        try:
            # 创建结果图像
            result_image = image.copy()
            
            # 为每个区域绘制边界框和字体信息
            for i, result in enumerate(font_results):
                x, y, w, h = result['bbox']
                
                # 绘制绿色边界框
                cv2.rectangle(result_image, (x, y), (x + w, y + h), (0, 255, 0), 2)
                
                # 绘制序号
                cv2.putText(result_image, str(i + 1), (x, y - 10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            
            # 保存结果图像
            output_path = os.path.join(self.debug_dir, "font_recognition_result.png")
            cv2.imwrite(output_path, result_image)
            
            # 生成详细对比图
            self._generate_font_comparison(font_results)
            
        except Exception as e:
            print(f"   ❌ 生成可视化失败: {e}")
    
    def _generate_font_comparison(self, font_results: List[Dict]):
        """生成字体对比图 - 每个文字单独生成图片"""
        try:
            if not font_results:
                return
            
            # 只处理前3个文本
            limited_results = font_results[:3]
            
            for idx, result in enumerate(limited_results):
                text = result['text']
                original_region = result['original_region']
                similarity_scores = result['similarity_scores']
                
                # 为每个文字创建单独的对比图
                fig, axes = plt.subplots(1, 4, figsize=(16, 4))
                
                # 显示原始文字
                axes[0].imshow(original_region, cmap='gray')
                axes[0].set_title(f"原文: {text}", fontsize=14, fontweight='bold')
                axes[0].axis('off')
                
                # 显示前3个最佳匹配
                for j in range(3):
                    if j < len(similarity_scores):
                        font_name, score = similarity_scores[j]
                        font_path = self.font_mapping.get(font_name, "")
                        
                        if font_path:
                            # 生成字体样本
                            font_sample = self._generate_font_sample(text, font_path)
                            if font_sample is not None:
                                axes[j+1].imshow(font_sample, cmap='gray')
                                axes[j+1].set_title(f"相似度: {score:.3f}", fontsize=12)
                                # 在底部添加字体名称
                                axes[j+1].text(0.5, -0.1, font_name, 
                                              ha='center', va='top', transform=axes[j+1].transAxes,
                                              fontsize=10, fontweight='bold')
                            else:
                                axes[j+1].text(0.5, 0.5, f"生成失败", 
                                                ha='center', va='center', transform=axes[j+1].transAxes,
                                                fontsize=10)
                                axes[j+1].set_title(f"相似度: {score:.3f}", fontsize=12)
                                axes[j+1].text(0.5, -0.1, font_name, 
                                              ha='center', va='top', transform=axes[j+1].transAxes,
                                              fontsize=10, fontweight='bold')
                        else:
                            axes[j+1].text(0.5, 0.5, f"字体文件\n未找到", 
                                            ha='center', va='center', transform=axes[j+1].transAxes,
                                            fontsize=10)
                            axes[j+1].set_title(f"相似度: {score:.3f}", fontsize=12)
                            axes[j+1].text(0.5, -0.1, font_name, 
                                          ha='center', va='top', transform=axes[j+1].transAxes,
                                          fontsize=10, fontweight='bold')
                        
                        axes[j+1].axis('off')
                    else:
                        axes[j+1].axis('off')
                
                plt.tight_layout()
                
                # 保存单独的对比图
                safe_text = "".join(c for c in text if c.isalnum() or c in "._-")
                if not safe_text:  # 如果没有安全字符，使用索引
                    safe_text = f"text_{idx+1}"
                
                comparison_path = os.path.join(self.debug_dir, f"font_comparison_{idx+1}_{safe_text}.png")
                plt.savefig(comparison_path, dpi=300, bbox_inches='tight')
                plt.close()
                
                print(f"💾 字体对比图已保存: {comparison_path}")
            
            print(f"✅ 共生成 {len(limited_results)} 张字体对比图")
            
        except Exception as e:
            print(f"   ❌ 生成字体对比图失败: {e}")
    
    def _save_detailed_results(self, image_path: str, font_results: List[Dict]):
        """保存详细结果"""
        try:
            # 准备保存数据
            save_data = {
                'image_path': image_path,
                'total_regions': len(font_results),
                'font_mapping_count': len(self.font_mapping),
                'results': []
            }
            
            for result in font_results:
                result_data = {
                    'text': result['text'],
                    'bbox': result['bbox'],
                    'matched_font': result['matched_font'],
                    'confidence': result['confidence'],
                    'supports_japanese': result['supports_japanese'],
                    'similarity_ranking': [
                        {'font_name': name, 'score': score} 
                        for name, score in result['similarity_scores']
                    ]
                }
                save_data['results'].append(result_data)
            
            # 保存JSON文件
            json_path = os.path.join(self.debug_dir, "font_recognition_data.json")
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, ensure_ascii=False, indent=2)
            
        except Exception as e:
            print(f"   ❌ 保存详细结果失败: {e}")


def main():
    """主函数"""
    import sys
    
    # 默认测试图像
    test_image_path = "../example.jpg"
    
    # 如果提供了命令行参数，使用指定的图像
    if len(sys.argv) > 1:
        test_image_path = sys.argv[1]
    
    # 检查图像是否存在
    if not os.path.exists(test_image_path):
        print(f"❌ 图像文件不存在: {test_image_path}")
        return
    
    # 创建测试器并运行测试
    tester = FontRecognitionTester()
    tester.test_font_recognition(test_image_path)


if __name__ == "__main__":
    main() 