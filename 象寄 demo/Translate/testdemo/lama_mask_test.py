#!/usr/bin/env python3
"""
LaMa 掩码修复测试脚本

用法:
1. 在 testdemo/ 目录下放置一张图像，图像上有黑色掩码遮盖需要擦除的区域
2. 运行脚本：python testdemo/lama_mask_test.py
3. 或指定图像：python testdemo/lama_mask_test.py --image your_image.jpg

功能:
- 自动检测图像中的黑色掩码区域 (RGB值接近(0,0,0))
- 使用LaMa模型进行高质量修复
- 支持多个独立的掩码区域
- 生成前后对比图像
- 可调节黑色检测阈值

注意: 确保掩码区域是纯黑色或接近黑色的像素
"""

import os
import sys
import argparse
import cv2
import numpy as np
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from mask_inpaint.factory import InpaintModelFactory
from mask_inpaint.base import InpaintResult
import time


class LamaMaskTester:
    """LaMa掩码修复测试器"""
    
    def __init__(self, device='mps', quality='high'):
        """
        初始化测试器
        
        Args:
            device: 计算设备 ('cpu', 'mps', 'cuda')
            quality: 修复质量 ('low', 'medium', 'high')
        """
        self.device = device
        self.quality = quality
        
        # 初始化LaMa模型
        print(f"🚀 初始化LaMa修复引擎 (设备: {device}, 质量: {quality})")
        self.config = {
            'device': device,
            'quality': quality,
            'model_path': 'models/lama',
            'input_size': 1024 if quality == 'high' else 512
        }
        
        try:
            self.inpaint_model = InpaintModelFactory.create_model('lama', self.config)
            if not self.inpaint_model.initialize_model():
                print("❌ LaMa模型初始化失败，回退到OpenCV")
                self.inpaint_model = InpaintModelFactory.create_model('opencv', {
                    'device': 'cpu',
                    'method': 'telea',
                    'quality': quality
                })
                self.inpaint_model.initialize_model()
        except Exception as e:
            print(f"❌ 模型初始化失败: {e}")
            print("回退到OpenCV引擎")
            self.inpaint_model = InpaintModelFactory.create_model('opencv', {
                'device': 'cpu',
                'method': 'telea',
                'quality': quality
            })
            self.inpaint_model.initialize_model()
    
    def detect_black_mask(self, image: np.ndarray, threshold: int = 30) -> np.ndarray:
        """
        检测图像中的黑色掩码区域
        
        Args:
            image: 输入图像 (BGR格式)
            threshold: 黑色检测阈值，越小越严格 (0-255)
            
        Returns:
            np.ndarray: 二值掩码，255表示需要修复的区域
        """
        print(f"🔍 检测黑色掩码区域 (阈值: {threshold})")
        
        # 转换为灰度图进行快速检测
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 创建黑色掩码：灰度值小于阈值的像素
        _, mask = cv2.threshold(gray, threshold, 255, cv2.THRESH_BINARY_INV)
        
        # 形态学操作清理掩码
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel, iterations=2)
        mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel, iterations=1)
        
        # 统计掩码信息
        mask_area = np.sum(mask > 0)
        total_area = mask.shape[0] * mask.shape[1]
        mask_ratio = mask_area / total_area * 100
        
        print(f"   掩码区域: {mask_area} 像素 ({mask_ratio:.1f}%)")
        
        return mask
    
    def analyze_mask_regions(self, mask: np.ndarray) -> list:
        """
        分析掩码中的独立区域
        
        Args:
            mask: 二值掩码
            
        Returns:
            list: 区域信息列表
        """
        # 查找轮廓
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        regions = []
        for i, contour in enumerate(contours):
            area = cv2.contourArea(contour)
            if area > 100:  # 过滤小区域
                x, y, w, h = cv2.boundingRect(contour)
                regions.append({
                    'id': i + 1,
                    'area': int(area),
                    'bbox': (x, y, w, h),
                    'contour': contour
                })
        
        # 按面积排序
        regions.sort(key=lambda r: r['area'], reverse=True)
        
        print(f"🎯 检测到 {len(regions)} 个掩码区域:")
        for region in regions:
            x, y, w, h = region['bbox']
            print(f"   区域{region['id']}: 面积={region['area']}, 位置=({x},{y}), 尺寸={w}×{h}")
        
        return regions
    
    def perform_inpainting(self, image: np.ndarray, mask: np.ndarray) -> InpaintResult:
        """
        执行图像修复
        
        Args:
            image: 原始图像
            mask: 掩码图像
            
        Returns:
            InpaintResult: 修复结果
        """
        print(f"🎨 开始LaMa修复 (引擎: {self.inpaint_model.model_name})")
        start_time = time.time()
        
        result = self.inpaint_model.inpaint(image, mask)
        
        if result.success:
            print(f"✅ 修复完成 | 耗时: {result.processing_time:.2f}s | 置信度: {result.confidence:.2f}")
        else:
            print(f"❌ 修复失败: {result.error_message}")
        
        return result
    
    def create_comparison_image(self, original: np.ndarray, mask: np.ndarray, 
                              inpainted: np.ndarray, regions: list) -> np.ndarray:
        """
        创建前后对比图像
        
        Args:
            original: 原始图像
            mask: 掩码图像
            inpainted: 修复后图像
            regions: 掩码区域信息
            
        Returns:
            np.ndarray: 对比图像
        """
        h, w = original.shape[:2]
        
        # 创建3列对比图
        comparison = np.zeros((h, w * 3, 3), dtype=np.uint8)
        
        # 第1列：原始图像
        comparison[:, :w] = original
        
        # 第2列：掩码可视化
        mask_colored = cv2.cvtColor(mask, cv2.COLOR_GRAY2BGR)
        mask_overlay = cv2.addWeighted(original, 0.7, mask_colored, 0.3, 0)
        comparison[:, w:w*2] = mask_overlay
        
        # 第3列：修复结果
        comparison[:, w*2:] = inpainted
        
        # 添加文字标注
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = max(0.6, min(w, h) / 1000)
        thickness = max(1, int(font_scale * 2))
        
        # 标题
        cv2.putText(comparison, "Original", (10, 30), font, font_scale, (255, 255, 255), thickness)
        cv2.putText(comparison, "Mask Overlay", (w + 10, 30), font, font_scale, (255, 255, 255), thickness)
        cv2.putText(comparison, "Inpainted", (w*2 + 10, 30), font, font_scale, (255, 255, 255), thickness)
        
        # 在掩码列标注区域信息
        y_offset = 60
        for region in regions[:5]:  # 最多显示5个区域
            text = f"Region {region['id']}: {region['area']}px"
            cv2.putText(comparison, text, (w + 10, y_offset), font, font_scale * 0.7, (0, 255, 0), thickness)
            y_offset += 25
        
        return comparison
    
    def save_results(self, output_dir: str, original: np.ndarray, mask: np.ndarray,
                    inpainted: np.ndarray, comparison: np.ndarray, image_name: str):
        """
        保存所有结果图像
        
        Args:
            output_dir: 输出目录
            original: 原始图像
            mask: 掩码图像
            inpainted: 修复图像
            comparison: 对比图像
            image_name: 图像名称
        """
        os.makedirs(output_dir, exist_ok=True)
        
        base_name = Path(image_name).stem
        
        # 保存各种图像
        files_saved = []
        
        # 原始掩码
        mask_path = os.path.join(output_dir, f"{base_name}_mask.png")
        cv2.imwrite(mask_path, mask)
        files_saved.append(mask_path)
        
        # 修复结果
        result_path = os.path.join(output_dir, f"{base_name}_inpainted.png")
        cv2.imwrite(result_path, inpainted)
        files_saved.append(result_path)
        
        # 对比图
        comparison_path = os.path.join(output_dir, f"{base_name}_comparison.png")
        cv2.imwrite(comparison_path, comparison)
        files_saved.append(comparison_path)
        
        print(f"📁 结果已保存到 {output_dir}:")
        for file_path in files_saved:
            print(f"   {os.path.basename(file_path)}")
    
    def process_image(self, image_path: str, black_threshold: int = 30, 
                     output_dir: str = "testdemo/lama_results") -> bool:
        """
        处理单张图像
        
        Args:
            image_path: 图像路径
            black_threshold: 黑色检测阈值
            output_dir: 输出目录
            
        Returns:
            bool: 处理是否成功
        """
        print(f"\n🖼️  处理图像: {image_path}")
        
        # 读取图像
        if not os.path.exists(image_path):
            print(f"❌ 图像文件不存在: {image_path}")
            return False
        
        image = cv2.imread(image_path)
        if image is None:
            print(f"❌ 无法读取图像: {image_path}")
            return False
        
        print(f"   图像尺寸: {image.shape[1]}×{image.shape[0]}")
        
        try:
            # 步骤1: 检测黑色掩码
            mask = self.detect_black_mask(image, black_threshold)
            
            # 步骤2: 分析掩码区域
            regions = self.analyze_mask_regions(mask)
            
            if len(regions) == 0:
                print(f"⚠️  未检测到黑色掩码区域 (阈值: {black_threshold})")
                print("   请检查图像是否包含黑色掩码，或调整阈值参数")
                return False
            
            # 步骤3: 执行修复
            result = self.perform_inpainting(image, mask)
            
            if not result.success:
                print(f"❌ 修复失败: {result.error_message}")
                return False
            
            # 步骤4: 创建对比图像
            comparison = self.create_comparison_image(image, mask, result.inpainted_image, regions)
            
            # 步骤5: 保存结果
            self.save_results(output_dir, image, mask, result.inpainted_image, 
                            comparison, os.path.basename(image_path))
            
            print(f"✅ 处理完成！")
            return True
            
        except Exception as e:
            print(f"❌ 处理过程中出错: {e}")
            return False


def find_test_images(test_dir: str) -> list:
    """查找测试目录中的图像文件"""
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']
    images = []
    
    if os.path.exists(test_dir):
        for file in os.listdir(test_dir):
            if any(file.lower().endswith(ext) for ext in image_extensions):
                images.append(os.path.join(test_dir, file))
    
    return sorted(images)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='LaMa掩码修复测试工具')
    parser.add_argument('--image', '-i', help='输入图像路径')
    parser.add_argument('--device', choices=['cpu', 'mps', 'cuda'], default='mps', 
                       help='计算设备 (默认: mps)')
    parser.add_argument('--quality', choices=['low', 'medium', 'high'], default='high',
                       help='修复质量 (默认: high)')
    parser.add_argument('--threshold', '-t', type=int, default=30,
                       help='黑色检测阈值 0-255 (默认: 30)')
    parser.add_argument('--output-dir', '-o', default='testdemo/lama_results',
                       help='输出目录 (默认: testdemo/lama_results)')
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("🎭 LaMa 掩码修复测试工具")
    print("=" * 60)
    print(f"设备: {args.device} | 质量: {args.quality} | 黑色阈值: {args.threshold}")
    
    # 初始化测试器
    try:
        tester = LamaMaskTester(device=args.device, quality=args.quality)
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return 1
    
    # 确定要处理的图像
    if args.image:
        # 处理指定图像
        if not tester.process_image(args.image, args.threshold, args.output_dir):
            return 1
    else:
        # 处理testdemo目录下的所有图像
        test_dir = "testdemo"
        images = find_test_images(test_dir)
        
        if not images:
            print(f"\n⚠️  未在 {test_dir} 目录下找到图像文件")
            print("请将带有黑色掩码的图像放入该目录，或使用 --image 指定图像路径")
            return 1
        
        print(f"\n📂 在 {test_dir} 中发现 {len(images)} 张图像:")
        for img in images:
            print(f"   {os.path.basename(img)}")
        
        success_count = 0
        for image_path in images:
            if tester.process_image(image_path, args.threshold, args.output_dir):
                success_count += 1
            print()  # 空行分隔
        
        print(f"🎯 处理完成: {success_count}/{len(images)} 张图像成功")
    
    print("\n💡 使用提示:")
    print("1. 确保掩码区域是黑色或深色 (RGB接近0,0,0)")
    print("2. 如果检测不到掩码，尝试调整 --threshold 参数")
    print("3. 较小的阈值检测更严格的黑色，较大的阈值检测更宽松")
    
    return 0


if __name__ == "__main__":
    sys.exit(main()) 