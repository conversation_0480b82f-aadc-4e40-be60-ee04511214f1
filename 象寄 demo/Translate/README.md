# 图像翻译项目

这是一个基于OCR和字体匹配的中日文图像翻译项目，采用模块化架构设计。

## 项目结构

```
Translate/
├── main.py                # 主程序入口
├── pipeline.py            # 翻译流水线
├── example.jpg            # 示例图片
├── *.jpg                  # 其他测试图片
├── README.md              # 项目说明
├── processors/            # 处理器模块
│   ├── __init__.py
│   ├── ocr_processor.py      # OCR文字识别
│   ├── translation_processor.py # 翻译处理
│   ├── font_processor.py     # 字体处理
│   ├── layout_processor.py   # 布局分析
│   ├── inpaint_processor.py  # 图像修复
│   └── renderer.py          # 渲染器
├── translation_models/    # 翻译模型
│   ├── __init__.py
│   ├── base.py
│   ├── factory.py
│   └── openai_model.py
├── mask_inpaint/         # 图像修复模型
│   ├── __init__.py
│   ├── base.py
│   ├── factory.py
│   ├── opencv_model.py
│   └── lama_model.py
├── utils/                # 工具模块
│   ├── __init__.py
│   ├── enhanced_font_manager.py
│   ├── font_downloader.py
│   ├── font_scanner.py
│   ├── download_fonts.py
│   └── system_fonts.json
├── testdemo/             # 测试演示
│   ├── __init__.py
│   ├── test_variable_font.py     # 可变字体测试
│   ├── test_measure_rendered_text_height.py # 高度测试
│   └── compare_weights.py        # 字体粗细对比
├── fonts/                # 字体文件目录
│   ├── 思源黑体/
│   ├── NotoSansSC/
│   └── ... (其他字体)
├── output/               # 输出目录
└── debug_images/         # 调试图像目录
```

## 主要功能

1. **OCR文字识别** - 使用PaddleOCR识别图像中的中文文字
2. **智能翻译** - 支持OpenAI API的高质量中日翻译
3. **字体处理** - 智能字体匹配和可变字体支持
4. **布局分析** - 精确的文本布局分析和重建
5. **图像修复** - 使用LAMA模型智能移除原文
6. **高度匹配** - 确保译文与原文在视觉高度上一致

## 使用方法

### 基本翻译
```bash
# 使用默认设置翻译图片
python main.py example.jpg

# 启用OpenAI翻译
python main.py example.jpg --openai-translation

# 启用所有调试输出
python main.py example.jpg --enable-all-debug
```

### 配置选项
```bash
# 查看所有可用选项
python main.py --help

# 自定义输出目录
python main.py example.jpg --output-dir custom_output

# 启用特定调试功能
python main.py example.jpg --enable-ocr-debug --enable-layout-debug
```

### 测试功能
```bash
# 测试可变字体
cd testdemo
python test_variable_font.py

# 测试文本高度测量
python test_measure_rendered_text_height.py

# 对比不同字体粗细
python compare_weights.py
```

## 架构特点

- **模块化设计** - 各功能模块独立，便于维护和扩展
- **插件架构** - 翻译模型和图像修复模型支持插件式扩展
- **调试友好** - 丰富的调试输出和可视化功能
- **配置灵活** - 支持命令行参数和配置文件

## 输出说明

- **主要输出**: `output/` 目录
  - `final_translated.png` - 最终翻译结果
  - `translation_debug.png` - 翻译过程可视化

- **调试输出**: `debug_images/` 目录
  - `ocr_processor/` - OCR调试图像
  - `layout_processor/` - 布局分析调试
  - `inpaint_processor/` - 图像修复调试

## 依赖环境

- Python 3.8+
- OpenCV
- PaddleOCR
- Pillow (需要支持可变字体的版本)
- NumPy
- scikit-learn
- OpenAI (可选，用于高质量翻译)

## 注意事项

1. 确保已安装支持可变字体的Pillow版本
2. 思源黑体可变字体文件需放在 `fonts/思源黑体/SourceHanSans-VF.otf`
3. 如需最佳效果，建议使用高分辨率的输入图片
4. OpenAI翻译需要设置API密钥环境变量 