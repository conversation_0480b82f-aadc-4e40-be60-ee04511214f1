"""
测试配置文件
定义测试相关的配置和工具函数
"""
import os
import tempfile
import shutil
import numpy as np
from typing import Dict, Any


class TestConfig:
    """测试配置类"""
    
    # 测试用的默认配置
    DEFAULT_OCR_CONFIDENCE = 0.5
    DEFAULT_ALIGNMENT_THRESHOLD = 5
    DEFAULT_PROXIMITY_THRESHOLD = 50
    DEFAULT_FONT_WEIGHT = 400
    
    # 测试用的字体映射
    TEST_FONT_MAPPING = {
        "思源黑体": "fonts/SourceHanSans-VF.otf",
        "台北黑体": "fonts/TaipeiSans-Bold.ttf",
        "Noto Sans": "fonts/NotoSansSC-Black.ttf"
    }
    
    # 测试用的翻译字典
    TEST_TRANSLATION_DICT = {
        "测试": "テスト",
        "你好": "こんにちは",
        "世界": "世界",
        "4D高回弹记忆棉": "4D高反発記憶フォーム",
        "久睡不塌": "長時間でも沈まない",
        "适用更久": "長く使える",
        "0压感": "ゼロ圧感"
    }


class TestUtils:
    """测试工具类"""
    
    @staticmethod
    def create_temp_directory() -> str:
        """创建临时目录"""
        return tempfile.mkdtemp()
    
    @staticmethod
    def cleanup_temp_directory(temp_dir: str):
        """清理临时目录"""
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir, ignore_errors=True)
    
    @staticmethod
    def create_test_image(width: int = 300, height: int = 200, channels: int = 3) -> np.ndarray:
        """创建测试图像"""
        return np.zeros((height, width, channels), dtype=np.uint8)
    
    @staticmethod
    def create_test_polygon(x: int, y: int, w: int, h: int) -> np.ndarray:
        """创建测试多边形"""
        return np.array([[x, y], [x+w, y], [x+w, y+h], [x, y+h]])
    
    @staticmethod
    def create_test_font_files(base_dir: str, font_mapping: Dict[str, str]):
        """创建测试字体文件"""
        for font_name, font_path in font_mapping.items():
            full_path = os.path.join(base_dir, font_path)
            os.makedirs(os.path.dirname(full_path), exist_ok=True)
            with open(full_path, 'w') as f:
                f.write(f"dummy font file for {font_name}")
    
    @staticmethod
    def create_test_translation_dict_file(file_path: str, translation_dict: Dict[str, str]):
        """创建测试翻译字典文件"""
        import json
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(translation_dict, f, ensure_ascii=False, indent=2)
    
    @staticmethod
    def save_test_image(image: np.ndarray, file_path: str):
        """保存测试图像"""
        import cv2
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        cv2.imwrite(file_path, image)


class MockObjects:
    """模拟对象工厂"""
    
    @staticmethod
    def create_mock_config_manager(base_dir: str = None):
        """创建模拟配置管理器"""
        from unittest.mock import Mock
        
        mock_config_manager = Mock()
        mock_config_manager.config = Mock()
        mock_config_manager.config.ocr_confidence_threshold = TestConfig.DEFAULT_OCR_CONFIDENCE
        mock_config_manager.config.alignment_threshold = TestConfig.DEFAULT_ALIGNMENT_THRESHOLD
        mock_config_manager.config.proximity_threshold = TestConfig.DEFAULT_PROXIMITY_THRESHOLD
        mock_config_manager.config.default_font_weight = TestConfig.DEFAULT_FONT_WEIGHT
        mock_config_manager.config.fonts_dir = "fonts"
        mock_config_manager.config.output_dir = "output"
        
        mock_config_manager.font_mapping = TestConfig.TEST_FONT_MAPPING.copy()
        mock_config_manager.translation_dict = TestConfig.TEST_TRANSLATION_DICT.copy()
        
        if base_dir:
            mock_config_manager.get_fonts_dir.return_value = os.path.join(base_dir, "fonts")
            mock_config_manager.get_output_dir.return_value = os.path.join(base_dir, "output")
            mock_config_manager.ensure_output_dir.return_value = os.path.join(base_dir, "output")
        
        return mock_config_manager
    
    @staticmethod
    def create_mock_text_region(region_id: int, x: int, y: int, w: int, h: int, 
                               text: str, is_chinese: bool = True):
        """创建模拟文字区域"""
        from models.data_models import TextRegion
        poly = TestUtils.create_test_polygon(x, y, w, h)
        return TextRegion.from_ocr_result(region_id, poly, text, 0.9, is_chinese)
    
    @staticmethod
    def create_mock_font_result(region_id: int, text: str, font_name: str = "思源黑体"):
        """创建模拟字体匹配结果"""
        from models.data_models import FontMatchResult
        return FontMatchResult(
            text=text,
            matched_font=font_name,
            font_path=f"/path/to/{font_name}.otf",
            confidence=0.8,
            supports_japanese=True,
            region_id=region_id
        )
    
    @staticmethod
    def create_mock_style_info(font_size: int = 24):
        """创建模拟样式信息"""
        from models.data_models import StyleInfo
        return StyleInfo(
            estimated_font_size=font_size,
            color=(0, 0, 0),
            background_color=(255, 255, 255),
            is_bold=False,
            contrast_ratio=21.0
        )
    
    @staticmethod
    def create_mock_layout_result(layout_mode: str = "test_mode"):
        """创建模拟布局结果"""
        from models.data_models import LayoutResult
        return LayoutResult(
            layout_mode=layout_mode,
            regions=[],
            horizontal_alignment={'type': 'left', 'left_groups': [], 'center_groups': [], 'right_groups': []},
            vertical_distribution={'type': 'vertical', 'rows': 1, 'columns': 1},
            alignment_strategies=[]
        )


class TestAssertions:
    """测试断言工具"""
    
    @staticmethod
    def assert_processing_result_success(test_case, result, expected_data_type=None):
        """断言处理结果成功"""
        test_case.assertTrue(result.success, f"处理结果应该成功，但失败了: {result.error_message}")
        test_case.assertIsNotNone(result.data, "成功的处理结果应该包含数据")
        test_case.assertEqual(result.error_message, "", "成功的处理结果不应该有错误消息")
        
        if expected_data_type:
            test_case.assertIsInstance(result.data, expected_data_type, 
                                     f"数据类型应该是 {expected_data_type}")
    
    @staticmethod
    def assert_processing_result_failure(test_case, result, expected_error_substring=None):
        """断言处理结果失败"""
        test_case.assertFalse(result.success, "处理结果应该失败")
        test_case.assertIsNotNone(result.error_message, "失败的处理结果应该包含错误消息")
        test_case.assertNotEqual(result.error_message, "", "错误消息不应该为空")
        
        if expected_error_substring:
            test_case.assertIn(expected_error_substring, result.error_message,
                             f"错误消息应该包含 '{expected_error_substring}'")
    
    @staticmethod
    def assert_text_region_valid(test_case, region, expected_text=None):
        """断言文字区域有效"""
        test_case.assertIsNotNone(region.id, "区域ID不应该为空")
        test_case.assertIsNotNone(region.text, "区域文本不应该为空")
        test_case.assertIsNotNone(region.bbox, "区域边界框不应该为空")
        test_case.assertEqual(len(region.bbox), 4, "边界框应该有4个值")
        test_case.assertIsNotNone(region.center, "区域中心点不应该为空")
        test_case.assertEqual(len(region.center), 2, "中心点应该有2个值")
        
        if expected_text:
            test_case.assertEqual(region.text, expected_text, f"区域文本应该是 '{expected_text}'")


# 测试数据常量
TEST_CHINESE_TEXTS = [
    "4D高回弹记忆棉",
    "久睡不塌",
    "适用更久",
    "0压感",
    "高回弹海绵",
    "深度分散压力",
    "不易塌陷"
]

TEST_MIXED_TEXTS = [
    ("4D高回弹记忆棉", True),
    ("WiFi密码", True),
    ("CPU温度45°C", True),
    ("iPhone15", False),
    ("100%", False),
    ("ABC", False),
    ("123", False)
]

TEST_FONT_NAMES = [
    "思源黑体",
    "台北黑体",
    "Noto Sans SC",
    "微软雅黑"
]
