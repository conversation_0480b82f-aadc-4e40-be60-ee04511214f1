"""
翻译处理器测试
"""
import unittest
import numpy as np
from unittest.mock import Mock, patch
from models.data_models import TextRegion, FontMatchResult, LayoutResult, StyleInfo
from processors.translation_processor import TranslationProcessor


class TestTranslationProcessor(unittest.TestCase):
    """翻译处理器测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 模拟配置管理器
        with patch('processors.translation_processor.get_config_manager') as mock_get_config:
            mock_config_manager = Mock()
            mock_config_manager.translation_dict = {
                "测试": "テスト",
                "你好": "こんにちは",
                "世界": "世界",
                "4D高回弹记忆棉": "4D高反発記憶フォーム",
                "久睡不塌": "長時間でも沈まない"
            }
            mock_config_manager.font_mapping = {
                "思源黑体": "/path/to/font.otf"
            }
            mock_get_config.return_value = mock_config_manager
            
            self.translation_processor = TranslationProcessor()
            
    def create_test_region(self, region_id, x, y, w, h, text):
        """创建测试用的文字区域"""
        poly = np.array([[x, y], [x+w, y], [x+w, y+h], [x, y+h]])
        return TextRegion.from_ocr_result(region_id, poly, text, 0.9, True)
        
    def create_test_font_result(self, region_id, text, font_name="思源黑体"):
        """创建测试用的字体匹配结果"""
        return FontMatchResult(
            text=text,
            matched_font=font_name,
            font_path="/path/to/font.otf",
            confidence=0.8,
            supports_japanese=True,
            region_id=region_id
        )
        
    def create_test_layout_result(self):
        """创建测试用的布局结果"""
        return LayoutResult(
            layout_mode='test_mode',
            regions=[],
            horizontal_alignment={'type': 'left'},
            vertical_distribution={'type': 'vertical'},
            alignment_strategies=[]
        )
        
    def test_translate_text_direct_match(self):
        """测试直接匹配翻译"""
        # 测试直接匹配
        result = self.translation_processor._translate_text("测试")
        self.assertEqual(result, "テスト")
        
        result = self.translation_processor._translate_text("你好")
        self.assertEqual(result, "こんにちは")
        
    def test_translate_text_with_quotes(self):
        """测试带引号的文本翻译"""
        # 测试去除引号后匹配
        result = self.translation_processor._translate_text('"测试"')
        self.assertEqual(result, "テスト")
        
        result = self.translation_processor._translate_text("'你好'")
        self.assertEqual(result, "こんにちは")
        
        result = self.translation_processor._translate_text('"测试"')
        self.assertEqual(result, "テスト")
        
    def test_translate_text_no_match(self):
        """测试无匹配的文本翻译"""
        # 测试未找到翻译的情况
        result = self.translation_processor._translate_text("未知文本")
        self.assertEqual(result, "未知文本")  # 返回原文
        
    def test_extract_text_style(self):
        """测试文字样式提取"""
        # 创建测试图像
        test_image = np.zeros((100, 200, 3), dtype=np.uint8)
        test_image[20:50, 30:120] = [50, 50, 50]  # 添加一些灰色区域
        
        # 创建测试多边形
        poly = np.array([[30, 20], [120, 20], [120, 50], [30, 50]])
        
        style_info = self.translation_processor._extract_text_style(test_image, poly)
        
        # 验证样式信息
        self.assertIsInstance(style_info, StyleInfo)
        self.assertGreater(style_info.estimated_font_size, 0)
        self.assertEqual(len(style_info.color), 3)
        self.assertEqual(len(style_info.background_color), 3)
        self.assertIn(style_info.is_bold, [True, False])  # 检查是布尔值
        self.assertGreater(style_info.contrast_ratio, 0)
        
    def test_extract_text_style_invalid_region(self):
        """测试无效区域的样式提取"""
        test_image = np.zeros((100, 200, 3), dtype=np.uint8)
        
        # 创建超出图像边界的多边形
        poly = np.array([[250, 250], [300, 250], [300, 280], [250, 280]])
        
        style_info = self.translation_processor._extract_text_style(test_image, poly)
        
        # 应该返回默认样式
        self.assertEqual(style_info.estimated_font_size, 24)
        self.assertEqual(style_info.color, (0, 0, 0))
        self.assertEqual(style_info.background_color, (255, 255, 255))
        
    def test_calculate_contrast_ratio(self):
        """测试对比度计算"""
        # 黑白对比（最高对比度）
        ratio = self.translation_processor._calculate_contrast_ratio((0, 0, 0), (255, 255, 255))
        self.assertAlmostEqual(ratio, 21.0, places=1)
        
        # 相同颜色（最低对比度）
        ratio = self.translation_processor._calculate_contrast_ratio((128, 128, 128), (128, 128, 128))
        self.assertAlmostEqual(ratio, 1.0, places=1)
        
        # 中等对比度
        ratio = self.translation_processor._calculate_contrast_ratio((0, 0, 0), (128, 128, 128))
        self.assertGreater(ratio, 1.0)
        self.assertLess(ratio, 21.0)
        
    def test_process_translation_success(self):
        """测试成功的翻译处理"""
        # 创建测试数据
        test_image = np.zeros((200, 300, 3), dtype=np.uint8)
        
        regions = [
            self.create_test_region(1, 10, 10, 100, 30, "测试"),
            self.create_test_region(2, 10, 50, 120, 30, "你好")
        ]
        
        font_results = [
            self.create_test_font_result(1, "测试"),
            self.create_test_font_result(2, "你好")
        ]
        
        layout_result = self.create_test_layout_result()
        
        # 模拟样式提取
        with patch.object(self.translation_processor, '_extract_text_style') as mock_extract_style:
            mock_style = StyleInfo(24, (0, 0, 0), (255, 255, 255), False, 21.0)
            mock_extract_style.return_value = mock_style
            
            result = self.translation_processor.process_translation(
                test_image, regions, font_results, layout_result
            )
            
            # 验证结果
            self.assertTrue(result.success)
            translation_results = result.data
            self.assertEqual(len(translation_results), 2)
            
            # 验证翻译结果
            self.assertEqual(translation_results[0].original_text, "测试")
            self.assertEqual(translation_results[0].translated_text, "テスト")
            self.assertEqual(translation_results[1].original_text, "你好")
            self.assertEqual(translation_results[1].translated_text, "こんにちは")
            
    def test_process_translation_no_matches(self):
        """测试无翻译匹配的情况"""
        test_image = np.zeros((200, 300, 3), dtype=np.uint8)
        
        # 创建无法翻译的文本区域
        regions = [
            self.create_test_region(1, 10, 10, 100, 30, "无法翻译的文本")
        ]
        
        font_results = [
            self.create_test_font_result(1, "无法翻译的文本")
        ]
        
        layout_result = self.create_test_layout_result()
        
        result = self.translation_processor.process_translation(
            test_image, regions, font_results, layout_result
        )
        
        # 应该返回空的翻译结果
        self.assertTrue(result.success)
        self.assertEqual(len(result.data), 0)
        
    def test_process_translation_non_chinese_regions(self):
        """测试处理非中文区域"""
        test_image = np.zeros((200, 300, 3), dtype=np.uint8)
        
        # 创建非中文区域
        poly = np.array([[10, 10], [110, 10], [110, 40], [10, 40]])
        english_region = TextRegion.from_ocr_result(1, poly, "English", 0.9, False)
        regions = [english_region]
        
        font_results = []
        layout_result = self.create_test_layout_result()
        
        result = self.translation_processor.process_translation(
            test_image, regions, font_results, layout_result
        )
        
        # 应该跳过非中文区域
        self.assertTrue(result.success)
        self.assertEqual(len(result.data), 0)
        
    def test_apply_layout_aware_strategy(self):
        """测试布局感知分组策略"""
        # 创建测试翻译结果
        translation_results = [
            Mock(bbox=(10, 10, 100, 30), group_key="", group_scale_factor=1.0),
            Mock(bbox=(10, 50, 120, 30), group_key="", group_scale_factor=1.0),
            Mock(bbox=(10, 100, 90, 30), group_key="", group_scale_factor=1.0)
        ]
        
        test_image = np.zeros((200, 300, 3), dtype=np.uint8)
        layout_result = self.create_test_layout_result()
        
        processed_results = self.translation_processor._apply_layout_aware_strategy(
            translation_results, test_image, layout_result
        )
        
        # 验证分组结果
        self.assertEqual(len(processed_results), 3)
        
        # 验证每个结果都有分组信息
        for result in processed_results:
            self.assertIsNotNone(result.group_key)
            self.assertIsNotNone(result.group_scale_factor)
            
    def test_calculate_group_scale_factor(self):
        """测试分组缩放因子计算"""
        # 创建测试分组结果
        group_results = [
            Mock(style_info=Mock(estimated_font_size=20)),
            Mock(style_info=Mock(estimated_font_size=22)),
            Mock(style_info=Mock(estimated_font_size=18))
        ]
        
        scale_factor = self.translation_processor._calculate_group_scale_factor(group_results)

        # 平均字体大小为20，根据实现可能返回0.9或1.0
        self.assertIn(scale_factor, [0.9, 1.0])
        
        # 测试大字体
        group_results = [
            Mock(style_info=Mock(estimated_font_size=45)),
            Mock(style_info=Mock(estimated_font_size=50))
        ]
        
        scale_factor = self.translation_processor._calculate_group_scale_factor(group_results)
        self.assertEqual(scale_factor, 1.1)
        
        # 测试中等字体
        group_results = [
            Mock(style_info=Mock(estimated_font_size=30)),
            Mock(style_info=Mock(estimated_font_size=35))
        ]
        
        scale_factor = self.translation_processor._calculate_group_scale_factor(group_results)
        self.assertEqual(scale_factor, 1.0)
        
    def test_default_style_info(self):
        """测试默认样式信息"""
        default_style = self.translation_processor._default_style_info()
        
        self.assertEqual(default_style.estimated_font_size, 24)
        self.assertEqual(default_style.color, (0, 0, 0))
        self.assertEqual(default_style.background_color, (255, 255, 255))
        self.assertFalse(default_style.is_bold)
        self.assertEqual(default_style.contrast_ratio, 21.0)


if __name__ == '__main__':
    unittest.main()
