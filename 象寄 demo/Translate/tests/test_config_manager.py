"""
配置管理器测试
"""
import unittest
import tempfile
import os
import json
from config.settings import ConfigManager


class TestConfigManager(unittest.TestCase):
    """配置管理器测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 创建临时目录作为测试基础目录
        self.temp_dir = tempfile.mkdtemp()
        self.config_manager = ConfigManager(base_dir=self.temp_dir)
        
    def tearDown(self):
        """测试后清理"""
        # 清理临时目录
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
        
    def test_config_initialization(self):
        """测试配置初始化"""
        config = self.config_manager.config
        
        # 检查默认配置
        self.assertEqual(config.ocr_confidence_threshold, 0.5)
        self.assertEqual(config.alignment_threshold, 5)
        self.assertEqual(config.proximity_threshold, 50)
        self.assertEqual(config.default_font_weight, 400)
        self.assertEqual(config.fonts_dir, "fonts")
        self.assertEqual(config.output_dir, "output")
        
    def test_font_mapping_loading(self):
        """测试字体映射加载"""
        # 创建测试字体目录结构
        fonts_dir = os.path.join(self.temp_dir, "fonts")
        os.makedirs(fonts_dir, exist_ok=True)
        
        # 创建测试字体文件
        test_font_dirs = [
            "思源黑体",
            "台北黑体", 
            "NotoSansSC"
        ]
        
        for font_dir in test_font_dirs:
            os.makedirs(os.path.join(fonts_dir, font_dir), exist_ok=True)
            
        # 创建字体文件
        font_files = [
            "思源黑体/SourceHanSans-VF.otf",
            "台北黑体/TaipeiSans-Bold.ttf",
            "NotoSansSC/NotoSansSC-Black.ttf"
        ]
        
        for font_file in font_files:
            font_path = os.path.join(fonts_dir, font_file)
            with open(font_path, 'w') as f:
                f.write("dummy font file")
        
        # 重新初始化配置管理器
        config_manager = ConfigManager(base_dir=self.temp_dir)
        font_mapping = config_manager.font_mapping
        
        # 检查字体映射
        self.assertIn('思源黑体', font_mapping)
        self.assertIn('台北黑体 TC Bold', font_mapping)
        self.assertIn('Noto Sans SC Black', font_mapping)
        
    def test_translation_dict_loading(self):
        """测试翻译字典加载"""
        # 创建测试翻译字典文件
        test_dict = {
            "测试": "テスト",
            "你好": "こんにちは",
            "世界": "世界"
        }
        
        dict_path = os.path.join(self.temp_dir, "translation_dictionary.json")
        with open(dict_path, 'w', encoding='utf-8') as f:
            json.dump(test_dict, f, ensure_ascii=False)
        
        # 重新初始化配置管理器
        config_manager = ConfigManager(base_dir=self.temp_dir)
        translation_dict = config_manager.translation_dict
        
        # 检查翻译字典
        self.assertEqual(translation_dict["测试"], "テスト")
        self.assertEqual(translation_dict["你好"], "こんにちは")
        self.assertEqual(translation_dict["世界"], "世界")
        
    def test_config_update(self):
        """测试配置更新"""
        # 更新配置
        self.config_manager.update_config(
            ocr_confidence_threshold=0.8,
            default_font_weight=700,
            alignment_threshold=10
        )
        
        config = self.config_manager.config
        self.assertEqual(config.ocr_confidence_threshold, 0.8)
        self.assertEqual(config.default_font_weight, 700)
        self.assertEqual(config.alignment_threshold, 10)
        
    def test_font_mapping_addition(self):
        """测试字体映射添加"""
        # 创建测试字体文件
        test_font_path = os.path.join(self.temp_dir, "test_font.ttf")
        with open(test_font_path, 'w') as f:
            f.write("test font")
        
        # 添加字体映射
        self.config_manager.add_font_mapping("测试字体", test_font_path)
        
        font_mapping = self.config_manager.font_mapping
        self.assertIn("测试字体", font_mapping)
        self.assertEqual(font_mapping["测试字体"], test_font_path)
        
    def test_translation_addition(self):
        """测试翻译添加"""
        # 添加翻译
        self.config_manager.add_translation("新词", "新しい言葉")
        
        translation_dict = self.config_manager.translation_dict
        self.assertEqual(translation_dict["新词"], "新しい言葉")
        
    def test_directory_operations(self):
        """测试目录操作"""
        # 测试获取字体目录
        fonts_dir = self.config_manager.get_fonts_dir()
        expected_fonts_dir = os.path.join(self.temp_dir, "fonts")
        self.assertEqual(fonts_dir, expected_fonts_dir)
        
        # 测试获取输出目录
        output_dir = self.config_manager.get_output_dir()
        expected_output_dir = os.path.join(self.temp_dir, "output")
        self.assertEqual(output_dir, expected_output_dir)
        
        # 测试确保输出目录存在
        ensured_output_dir = self.config_manager.ensure_output_dir()
        self.assertTrue(os.path.exists(ensured_output_dir))
        self.assertEqual(ensured_output_dir, expected_output_dir)
        
    def test_translation_dict_save(self):
        """测试翻译字典保存"""
        # 添加一些翻译
        self.config_manager.add_translation("保存测试", "保存テスト")
        
        # 保存翻译字典
        self.config_manager.save_translation_dict()
        
        # 检查文件是否创建
        dict_path = os.path.join(self.temp_dir, "translation_dictionary.json")
        self.assertTrue(os.path.exists(dict_path))
        
        # 检查文件内容
        with open(dict_path, 'r', encoding='utf-8') as f:
            saved_dict = json.load(f)
        
        self.assertIn("保存测试", saved_dict)
        self.assertEqual(saved_dict["保存测试"], "保存テスト")
        
    def test_invalid_config_update(self):
        """测试无效配置更新"""
        # 尝试更新不存在的配置项
        self.config_manager.update_config(invalid_param=123)
        
        # 配置应该保持不变
        config = self.config_manager.config
        self.assertFalse(hasattr(config, 'invalid_param'))
        
    def test_nonexistent_font_addition(self):
        """测试添加不存在的字体"""
        nonexistent_path = "/path/to/nonexistent/font.ttf"
        
        # 添加不存在的字体路径
        self.config_manager.add_font_mapping("不存在字体", nonexistent_path)
        
        # 字体映射中不应该包含这个字体
        font_mapping = self.config_manager.font_mapping
        self.assertNotIn("不存在字体", font_mapping)


if __name__ == '__main__':
    unittest.main()
