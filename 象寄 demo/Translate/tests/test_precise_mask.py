#!/usr/bin/env python3
"""
测试精确掩码生成功能
"""
import os
import sys
import cv2
import numpy as np
import unittest
from unittest.mock import Mock

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from processors.inpaint_processor import InpaintProcessor
from models.data_models import TextRegion
from config.settings import ConfigManager


class TestPreciseMask(unittest.TestCase):
    """测试精确掩码生成"""
    
    def setUp(self):
        """测试前准备"""
        # 创建模拟配置管理器
        self.mock_config_manager = Mock()
        self.mock_config_manager.config.enable_inpaint_debug = True
        self.mock_config_manager.ensure_debug_dir.return_value = "debug_images/inpaint_processor"
        self.mock_config_manager.get_inpaint_config.return_value = {
            'engine': 'opencv',
            'device': 'cpu'
        }
        
        # 创建InpaintProcessor实例
        self.processor = InpaintProcessor()
        self.processor.config_manager = self.mock_config_manager
    
    def test_generate_precise_text_mask(self):
        """测试精确文字掩码生成"""
        # 创建测试图像：白底黑字
        test_image = np.ones((100, 200, 3), dtype=np.uint8) * 255
        
        # 绘制一些文字区域（模拟文字）
        cv2.rectangle(test_image, (20, 20), (80, 60), (0, 0, 0), -1)  # 黑色矩形模拟文字
        cv2.rectangle(test_image, (120, 30), (180, 70), (50, 50, 50), -1)  # 灰色矩形模拟文字
        
        # 测试精确掩码生成
        precise_mask = self.processor._generate_precise_text_mask(test_image)
        
        # 验证结果
        self.assertIsNotNone(precise_mask, "精确掩码生成不应该返回None")
        self.assertEqual(precise_mask.shape, test_image.shape[:2], "掩码尺寸应该与图像一致")
        self.assertTrue(np.any(precise_mask > 0), "掩码应该包含非零像素")
        
        # 验证掩码覆盖了文字区域
        text_area1 = precise_mask[20:60, 20:80]
        text_area2 = precise_mask[30:70, 120:180]
        
        self.assertTrue(np.any(text_area1 > 0), "第一个文字区域应该被掩码覆盖")
        self.assertTrue(np.any(text_area2 > 0), "第二个文字区域应该被掩码覆盖")
    
    def test_create_text_mask_with_regions(self):
        """测试使用文字区域创建掩码"""
        # 创建测试图像
        test_image = np.ones((200, 300, 3), dtype=np.uint8) * 255
        cv2.rectangle(test_image, (50, 50), (150, 100), (0, 0, 0), -1)
        
        # 创建测试文字区域
        poly = [[50, 50], [150, 50], [150, 100], [50, 100]]
        region = TextRegion.from_ocr_result(0, poly, "测试文字", 0.9, True)
        
        # 测试掩码创建
        mask = self.processor._create_text_mask(test_image, [region])
        
        # 验证结果
        self.assertEqual(mask.shape, test_image.shape[:2], "掩码尺寸应该与图像一致")
        self.assertTrue(np.any(mask > 0), "掩码应该包含非零像素")
        
        # 验证调试信息
        self.assertTrue(hasattr(self.processor, '_mask_debug_info'), "应该生成调试信息")
        self.assertEqual(len(self.processor._mask_debug_info), 1, "应该有一个区域的调试信息")
    
    def test_precise_mask_fallback(self):
        """测试精确掩码生成失败时的回退机制"""
        # 创建空白图像（无文字内容）
        empty_image = np.ones((50, 50, 3), dtype=np.uint8) * 255
        
        # 测试精确掩码生成
        precise_mask = self.processor._generate_precise_text_mask(empty_image)
        
        # 应该返回None（因为没有文字内容）
        self.assertIsNone(precise_mask, "空白图像应该返回None")
    
    def test_mask_comparison_debug(self):
        """测试掩码对比调试功能"""
        # 创建测试图像和区域
        test_image = np.ones((100, 200, 3), dtype=np.uint8) * 255
        cv2.rectangle(test_image, (20, 20), (80, 60), (0, 0, 0), -1)
        
        poly = [[20, 20], [80, 20], [80, 60], [20, 60]]
        region = TextRegion.from_ocr_result(0, poly, "测试", 0.9, True)
        
        # 创建测试掩码
        mask = np.zeros(test_image.shape[:2], dtype=np.uint8)
        mask[20:60, 20:80] = 255
        
        # 设置调试信息
        self.processor._mask_debug_info = [{
            'text': '测试',
            'ocr_bbox': (20, 20, 60, 40),
            'precise_mask_area': 2400,
            'ocr_mask_area': 2400
        }]
        
        # 测试调试图像保存（不会实际保存文件，只测试逻辑）
        try:
            self.processor._save_mask_comparison_debug(
                test_image, mask, [region], "/tmp/test_debug"
            )
            # 如果没有异常，说明逻辑正确
            self.assertTrue(True)
        except Exception as e:
            # 预期可能因为目录不存在而失败，但逻辑应该正确
            self.assertIn("No such file or directory", str(e))


def main():
    """运行测试"""
    print("开始测试精确掩码生成功能...")
    
    # 运行单元测试
    unittest.main(verbosity=2)


if __name__ == '__main__':
    main()
