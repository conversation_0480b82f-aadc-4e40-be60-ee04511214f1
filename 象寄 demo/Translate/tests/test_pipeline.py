"""
翻译流水线测试
"""
import unittest
import tempfile
import os
import numpy as np
from unittest.mock import Mock, patch, MagicMock
from pipeline import TranslationPipeline
from models.data_models import ProcessingResult, OCRResult, TextRegion


class TestTranslationPipeline(unittest.TestCase):
    """翻译流水线测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        
        # 创建测试图像文件
        self.test_image_path = os.path.join(self.temp_dir, "test_image.jpg")
        test_image = np.zeros((200, 300, 3), dtype=np.uint8)
        
        # 使用OpenCV保存测试图像
        import cv2
        cv2.imwrite(self.test_image_path, test_image)
        
    def tearDown(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
        
    def create_mock_ocr_result(self):
        """创建模拟OCR结果"""
        poly = np.array([[10, 10], [110, 10], [110, 40], [10, 40]])
        chinese_region = TextRegion.from_ocr_result(1, poly, "测试文字", 0.9, True)
        
        return OCRResult(
            dt_polys=[poly],
            rec_texts=["测试文字"],
            rec_scores=[0.9],
            chinese_regions=[chinese_region],
            other_regions=[]
        )
        
    @patch('pipeline.get_config_manager')
    def test_pipeline_initialization(self, mock_get_config):
        """测试流水线初始化"""
        # 模拟配置管理器
        mock_config_manager = Mock()
        mock_config_manager.config = Mock()
        mock_config_manager.ensure_output_dir = Mock()
        mock_get_config.return_value = mock_config_manager
        
        # 模拟各个处理器
        with patch('pipeline.OCRProcessor'), \
             patch('pipeline.FontProcessor'), \
             patch('pipeline.TranslationProcessor'), \
             patch('pipeline.InpaintProcessor'), \
             patch('pipeline.Renderer'), \
             patch('pipeline.LayoutProcessor'), \
             patch('pipeline.Visualizer'):
            
            pipeline = TranslationPipeline(font_weight=600, enable_debug=True)
            
            # 验证初始化
            self.assertIsNotNone(pipeline.ocr_processor)
            self.assertIsNotNone(pipeline.font_processor)
            self.assertIsNotNone(pipeline.translation_processor)
            self.assertIsNotNone(pipeline.inpaint_processor)
            self.assertIsNotNone(pipeline.renderer)
            self.assertIsNotNone(pipeline.layout_processor)
            self.assertIsNotNone(pipeline.visualizer)
            
    def test_process_image_file_not_found(self):
        """测试处理不存在的图像文件"""
        with patch('pipeline.get_config_manager'):
            pipeline = TranslationPipeline()
            
            result = pipeline.process_image("nonexistent.jpg")
            
            self.assertFalse(result.success)
            self.assertIn("图像文件不存在", result.error_message)
            
    @patch('pipeline.get_config_manager')
    def test_process_image_no_chinese_text(self, mock_get_config):
        """测试处理无中文文字的图像"""
        # 模拟配置
        mock_config_manager = Mock()
        mock_config_manager.config = Mock()
        mock_config_manager.ensure_output_dir = Mock()
        mock_get_config.return_value = mock_config_manager
        
        # 创建无中文文字的OCR结果
        ocr_result = OCRResult(
            dt_polys=[],
            rec_texts=[],
            rec_scores=[],
            chinese_regions=[],
            other_regions=[]
        )
        
        with patch('pipeline.OCRProcessor') as mock_ocr_cls, \
             patch('pipeline.FontProcessor'), \
             patch('pipeline.TranslationProcessor'), \
             patch('pipeline.InpaintProcessor'), \
             patch('pipeline.Renderer'), \
             patch('pipeline.LayoutProcessor'), \
             patch('pipeline.Visualizer'):
            
            # 模拟OCR处理器
            mock_ocr = Mock()
            mock_ocr.process_image.return_value = ProcessingResult.success_result(ocr_result)
            mock_ocr_cls.return_value = mock_ocr
            
            pipeline = TranslationPipeline()
            result = pipeline.process_image(self.test_image_path)
            
            # 验证结果
            self.assertTrue(result.success)
            self.assertIn("未检测到中文文字", result.data['message'])
            
    @patch('pipeline.get_config_manager')
    def test_process_image_success_flow(self, mock_get_config):
        """测试成功的图像处理流程"""
        # 模拟配置
        mock_config_manager = Mock()
        mock_config_manager.config = Mock()
        mock_config_manager.ensure_output_dir = Mock()
        mock_get_config.return_value = mock_config_manager
        
        # 创建模拟结果
        ocr_result = self.create_mock_ocr_result()
        layout_result = Mock()
        font_results = [Mock()]
        translation_results = [Mock()]
        inpainted_image = np.zeros((200, 300, 3), dtype=np.uint8)
        render_data = {
            'image': np.zeros((200, 300, 3), dtype=np.uint8),
            'render_log': [{'original': '测试', 'translated': 'テスト'}]
        }
        
        with patch('pipeline.OCRProcessor') as mock_ocr_cls, \
             patch('pipeline.FontProcessor') as mock_font_cls, \
             patch('pipeline.TranslationProcessor') as mock_trans_cls, \
             patch('pipeline.InpaintProcessor') as mock_inpaint_cls, \
             patch('pipeline.Renderer') as mock_render_cls, \
             patch('pipeline.LayoutProcessor') as mock_layout_cls, \
             patch('pipeline.Visualizer'):
            
            # 模拟各个处理器
            mock_ocr = Mock()
            mock_ocr.process_image.return_value = ProcessingResult.success_result(ocr_result)
            mock_ocr_cls.return_value = mock_ocr
            
            mock_layout = Mock()
            mock_layout.analyze_layout.return_value = ProcessingResult.success_result(layout_result)
            mock_layout_cls.return_value = mock_layout
            
            mock_font = Mock()
            mock_font.process_regions.return_value = ProcessingResult.success_result(font_results)
            mock_font_cls.return_value = mock_font
            
            mock_trans = Mock()
            mock_trans.process_translation.return_value = ProcessingResult.success_result(translation_results)
            mock_trans_cls.return_value = mock_trans
            
            mock_inpaint = Mock()
            mock_inpaint.process_inpainting.return_value = ProcessingResult.success_result(inpainted_image)
            mock_inpaint_cls.return_value = mock_inpaint
            
            mock_render = Mock()
            mock_render.render_translations.return_value = ProcessingResult.success_result(render_data)
            mock_render.save_final_image.return_value = "output/final.png"
            mock_render_cls.return_value = mock_render
            
            pipeline = TranslationPipeline()
            result = pipeline.process_image(self.test_image_path)
            
            # 验证成功结果
            self.assertTrue(result.success)
            self.assertIn('final_image', result.data)
            self.assertIn('render_log', result.data)
            self.assertIn('final_path', result.data)
            
            # 验证各个处理器被调用
            mock_ocr.process_image.assert_called_once()
            mock_layout.analyze_layout.assert_called_once()
            mock_font.process_regions.assert_called_once()
            mock_trans.process_translation.assert_called_once()
            mock_inpaint.process_inpainting.assert_called_once()
            mock_render.render_translations.assert_called_once()
            
    @patch('pipeline.get_config_manager')
    def test_process_image_ocr_failure(self, mock_get_config):
        """测试OCR处理失败的情况"""
        mock_config_manager = Mock()
        mock_config_manager.config = Mock()
        mock_config_manager.ensure_output_dir = Mock()
        mock_get_config.return_value = mock_config_manager
        
        with patch('pipeline.OCRProcessor') as mock_ocr_cls, \
             patch('pipeline.FontProcessor'), \
             patch('pipeline.TranslationProcessor'), \
             patch('pipeline.InpaintProcessor'), \
             patch('pipeline.Renderer'), \
             patch('pipeline.LayoutProcessor'), \
             patch('pipeline.Visualizer'):
            
            # 模拟OCR失败
            mock_ocr = Mock()
            mock_ocr.process_image.return_value = ProcessingResult.error_result("OCR处理失败")
            mock_ocr_cls.return_value = mock_ocr
            
            pipeline = TranslationPipeline()
            result = pipeline.process_image(self.test_image_path)
            
            # 验证失败结果
            self.assertFalse(result.success)
            self.assertEqual(result.error_message, "OCR处理失败")
            
    @patch('pipeline.get_config_manager')
    def test_process_image_no_translation_results(self, mock_get_config):
        """测试无翻译结果的情况"""
        mock_config_manager = Mock()
        mock_config_manager.config = Mock()
        mock_config_manager.ensure_output_dir = Mock()
        mock_get_config.return_value = mock_config_manager
        
        ocr_result = self.create_mock_ocr_result()
        layout_result = Mock()
        font_results = [Mock()]
        translation_results = []  # 空的翻译结果
        
        with patch('pipeline.OCRProcessor') as mock_ocr_cls, \
             patch('pipeline.FontProcessor') as mock_font_cls, \
             patch('pipeline.TranslationProcessor') as mock_trans_cls, \
             patch('pipeline.InpaintProcessor'), \
             patch('pipeline.Renderer'), \
             patch('pipeline.LayoutProcessor') as mock_layout_cls, \
             patch('pipeline.Visualizer'):
            
            # 模拟处理器
            mock_ocr = Mock()
            mock_ocr.process_image.return_value = ProcessingResult.success_result(ocr_result)
            mock_ocr_cls.return_value = mock_ocr
            
            mock_layout = Mock()
            mock_layout.analyze_layout.return_value = ProcessingResult.success_result(layout_result)
            mock_layout_cls.return_value = mock_layout
            
            mock_font = Mock()
            mock_font.process_regions.return_value = ProcessingResult.success_result(font_results)
            mock_font_cls.return_value = mock_font
            
            mock_trans = Mock()
            mock_trans.process_translation.return_value = ProcessingResult.success_result(translation_results)
            mock_trans_cls.return_value = mock_trans
            
            pipeline = TranslationPipeline()
            result = pipeline.process_image(self.test_image_path)
            
            # 验证结果
            self.assertTrue(result.success)
            self.assertIn("没有找到可翻译的文字", result.data['message'])
            
    @patch('pipeline.get_config_manager')
    def test_cleanup(self, mock_get_config):
        """测试资源清理"""
        mock_config_manager = Mock()
        mock_config_manager.config = Mock()
        mock_config_manager.ensure_output_dir = Mock()
        mock_get_config.return_value = mock_config_manager
        
        with patch('pipeline.OCRProcessor') as mock_ocr_cls, \
             patch('pipeline.FontProcessor'), \
             patch('pipeline.TranslationProcessor'), \
             patch('pipeline.InpaintProcessor'), \
             patch('pipeline.Renderer'), \
             patch('pipeline.LayoutProcessor'), \
             patch('pipeline.Visualizer'):
            
            mock_ocr = Mock()
            mock_ocr_cls.return_value = mock_ocr
            
            pipeline = TranslationPipeline()
            pipeline.cleanup()
            
            # 验证OCR处理器的cleanup被调用
            mock_ocr.cleanup.assert_called_once()
            
    @patch('pipeline.get_config_manager')
    def test_config_operations(self, mock_get_config):
        """测试配置操作"""
        mock_config_manager = Mock()
        mock_config_manager.config = Mock()
        mock_config_manager.ensure_output_dir = Mock()
        mock_config_manager.translation_dict = {"测试": "テスト"}
        mock_config_manager.font_mapping = {"字体": "/path"}
        mock_config_manager.get_fonts_dir.return_value = "/fonts"
        mock_config_manager.get_output_dir.return_value = "/output"
        mock_get_config.return_value = mock_config_manager
        
        with patch('pipeline.OCRProcessor'), \
             patch('pipeline.FontProcessor'), \
             patch('pipeline.TranslationProcessor'), \
             patch('pipeline.InpaintProcessor'), \
             patch('pipeline.Renderer'), \
             patch('pipeline.LayoutProcessor'), \
             patch('pipeline.Visualizer'):
            
            pipeline = TranslationPipeline()
            
            # 测试获取配置
            config = pipeline.get_config()
            self.assertIsNotNone(config)
            
            # 测试更新配置
            pipeline.update_config(ocr_confidence_threshold=0.8)
            mock_config_manager.update_config.assert_called_with(ocr_confidence_threshold=0.8)
            
            # 测试获取统计信息
            stats = pipeline.get_translation_stats()
            self.assertEqual(stats['translation_dict_size'], 1)
            self.assertEqual(stats['font_mapping_size'], 1)
            self.assertEqual(stats['fonts_dir'], "/fonts")
            self.assertEqual(stats['output_dir'], "/output")


if __name__ == '__main__':
    unittest.main()
