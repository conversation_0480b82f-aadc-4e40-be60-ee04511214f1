"""
OCR处理器测试
"""
import unittest
import tempfile
import os
import numpy as np
from unittest.mock import Mock, patch, MagicMock
from processors.ocr_processor import OCRProcessor


class TestOCRProcessor(unittest.TestCase):
    """OCR处理器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.ocr_processor = OCRProcessor()
        
    def test_chinese_text_detection(self):
        """测试中文文本检测"""
        test_cases = [
            # (文本, 期望结果)
            ("4D高回弹记忆棉", True),    # 中英数混合
            ("久睡不塌", True),          # 纯中文
            ("WiFi密码", True),          # 中英混合
            ("CPU温度45°C", True),       # 中文数字符号混合
            ("Hello世界", True),         # 英中混合
            ("iPhone15", False),        # 纯英数
            ("100%", False),            # 纯数字符号
            ("ABC", False),             # 纯英文
            ("123", False),             # 纯数字
            ("", False),                # 空字符串
            ("   ", False),             # 空白字符
        ]
        
        for text, expected in test_cases:
            with self.subTest(text=text):
                result = self.ocr_processor.is_chinese_text(text)
                self.assertEqual(result, expected, f"文本 '{text}' 检测结果错误")
                
    def test_chinese_text_mixed_content(self):
        """测试混合内容的中文检测"""
        # 测试中文占比较低的情况
        low_chinese_ratio = "A1B2C3中4D5E6F7G8H9"  # 中文占比约5%
        # 注意：由于包含中文字符，可能仍被识别为中文文本
        result = self.ocr_processor.is_chinese_text(low_chinese_ratio)
        # 这个测试可能因为实现逻辑而返回True，这是可以接受的
        
        # 测试中文占比适中的情况
        medium_chinese_ratio = "产品型号ABC123"  # 中文占比约40%
        self.assertTrue(self.ocr_processor.is_chinese_text(medium_chinese_ratio))
        
        # 测试中文占比较高的情况
        high_chinese_ratio = "这是一个测试ABC"  # 中文占比约70%
        self.assertTrue(self.ocr_processor.is_chinese_text(high_chinese_ratio))
        
    @patch('processors.ocr_processor.PaddleOCR')
    def test_ocr_instance_creation(self, mock_paddle_ocr):
        """测试OCR实例创建"""
        # 模拟PaddleOCR实例
        mock_ocr_instance = Mock()
        mock_paddle_ocr.return_value = mock_ocr_instance
        
        # 创建新的OCR处理器
        processor = OCRProcessor()
        
        # 获取OCR实例
        ocr_instance = processor._get_ocr_instance()
        
        # 验证PaddleOCR被正确调用
        mock_paddle_ocr.assert_called_once_with(
            use_doc_orientation_classify=False,
            use_doc_unwarping=False,
            use_textline_orientation=False
        )
        
        # 验证返回的是模拟实例
        self.assertEqual(ocr_instance, mock_ocr_instance)
        
    @patch('processors.ocr_processor.PaddleOCR')
    def test_ocr_instance_reuse(self, mock_paddle_ocr):
        """测试OCR实例复用"""
        mock_ocr_instance = Mock()
        mock_paddle_ocr.return_value = mock_ocr_instance
        
        processor = OCRProcessor()
        
        # 第一次获取实例
        instance1 = processor._get_ocr_instance()
        
        # 第二次获取实例
        instance2 = processor._get_ocr_instance()
        
        # 验证PaddleOCR只被调用一次
        mock_paddle_ocr.assert_called_once()
        
        # 验证返回的是同一个实例
        self.assertEqual(instance1, instance2)
        
    def test_parse_ocr_result(self):
        """测试OCR结果解析"""
        # 模拟OCR原始结果
        mock_raw_result = [{
            'dt_polys': [
                np.array([[10, 10], [100, 10], [100, 50], [10, 50]]),
                np.array([[10, 60], [80, 60], [80, 90], [10, 90]]),
                np.array([[10, 100], [120, 100], [120, 130], [10, 130]])
            ],
            'rec_texts': ['4D高回弹记忆棉', 'Hello', '测试文字'],
            'rec_scores': [0.95, 0.85, 0.90]
        }]
        
        # 解析结果
        ocr_result = self.ocr_processor._parse_ocr_result(mock_raw_result, 0.5)
        
        # 验证结果
        self.assertEqual(len(ocr_result.dt_polys), 3)
        self.assertEqual(len(ocr_result.rec_texts), 3)
        self.assertEqual(len(ocr_result.rec_scores), 3)
        self.assertEqual(ocr_result.chinese_count, 2)  # '4D高回弹记忆棉' 和 '测试文字'
        self.assertEqual(len(ocr_result.other_regions), 1)  # 'Hello'
        
        # 验证中文区域
        chinese_texts = [region.text for region in ocr_result.chinese_regions]
        self.assertIn('4D高回弹记忆棉', chinese_texts)
        self.assertIn('测试文字', chinese_texts)
        
        # 验证其他区域
        other_texts = [region.text for region in ocr_result.other_regions]
        self.assertIn('Hello', other_texts)
        
    def test_confidence_threshold_filtering(self):
        """测试置信度阈值过滤"""
        mock_raw_result = [{
            'dt_polys': [
                np.array([[10, 10], [100, 10], [100, 50], [10, 50]]),
                np.array([[10, 60], [80, 60], [80, 90], [10, 90]])
            ],
            'rec_texts': ['高置信度文字', '低置信度文字'],
            'rec_scores': [0.95, 0.3]  # 第二个低于阈值
        }]
        
        # 使用0.5的置信度阈值解析
        ocr_result = self.ocr_processor._parse_ocr_result(mock_raw_result, 0.5)
        
        # 验证只有高置信度的文字被保留
        self.assertEqual(ocr_result.chinese_count, 1)
        self.assertEqual(ocr_result.chinese_regions[0].text, '高置信度文字')
        self.assertEqual(ocr_result.chinese_regions[0].score, 0.95)
        
    def test_process_image_file_not_found(self):
        """测试处理不存在的图像文件"""
        result = self.ocr_processor.process_image("nonexistent.jpg")

        # 验证返回错误结果
        self.assertFalse(result.success)
        self.assertIn("图像文件不存在", result.error_message)
            
    @patch('os.path.exists')
    def test_process_image_success(self, mock_exists):
        """测试成功处理图像"""
        mock_exists.return_value = True
        
        # 模拟OCR实例和结果
        mock_ocr = Mock()
        mock_ocr.predict.return_value = [{
            'dt_polys': [np.array([[10, 10], [100, 10], [100, 50], [10, 50]])],
            'rec_texts': ['测试文字'],
            'rec_scores': [0.95]
        }]
        
        with patch.object(self.ocr_processor, '_get_ocr_instance') as mock_get_ocr:
            mock_get_ocr.return_value = mock_ocr
            
            result = self.ocr_processor.process_image("test.jpg")
            
            # 验证成功结果
            self.assertTrue(result.success)
            self.assertIsNotNone(result.data)
            self.assertEqual(result.data.chinese_count, 1)
            
    def test_cleanup(self):
        """测试资源清理"""
        # 设置一个模拟的OCR实例
        self.ocr_processor._ocr_instance = Mock()
        
        # 执行清理
        self.ocr_processor.cleanup()
        
        # 验证实例被清理
        self.assertIsNone(self.ocr_processor._ocr_instance)


if __name__ == '__main__':
    unittest.main()
