"""
字体处理器测试
"""
import unittest
import tempfile
import os
import numpy as np
from unittest.mock import Mock, patch, MagicMock
from models.data_models import TextRegion
from processors.font_processor import FontProcessor


class TestFontProcessor(unittest.TestCase):
    """字体处理器测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 创建临时目录
        self.temp_dir = tempfile.mkdtemp()
        
        # 模拟配置管理器
        with patch('processors.font_processor.get_config_manager') as mock_get_config:
            mock_config_manager = Mock()
            mock_config_manager.font_mapping = {
                '思源黑体': os.path.join(self.temp_dir, 'test_font1.otf'),
                '台北黑体': os.path.join(self.temp_dir, 'test_font2.ttf'),
                'Noto Sans': os.path.join(self.temp_dir, 'test_font3.ttf')
            }
            mock_get_config.return_value = mock_config_manager
            
            # 创建测试字体文件
            for font_path in mock_config_manager.font_mapping.values():
                with open(font_path, 'w') as f:
                    f.write("dummy font file")
            
            self.font_processor = FontProcessor()
            
    def tearDown(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
        
    def create_test_region(self, region_id, x, y, w, h, text):
        """创建测试用的文字区域"""
        poly = np.array([[x, y], [x+w, y], [x+w, y+h], [x, y+h]])
        return TextRegion.from_ocr_result(region_id, poly, text, 0.9, True)
        
    def test_process_regions_success(self):
        """测试成功处理文字区域"""
        # 创建测试图像
        test_image = np.zeros((200, 300, 3), dtype=np.uint8)
        
        # 创建测试区域
        regions = [
            self.create_test_region(1, 10, 10, 100, 30, "测试文字1"),
            self.create_test_region(2, 10, 50, 120, 30, "测试文字2")
        ]
        
        # 模拟字体匹配和日文支持检测
        with patch.object(self.font_processor, '_match_font') as mock_match_font, \
             patch.object(self.font_processor, '_test_japanese_support') as mock_jp_support:
            
            mock_match_font.side_effect = [('思源黑体', 0.85), ('台北黑体', 0.75)]
            mock_jp_support.return_value = True
            
            result = self.font_processor.process_regions(test_image, regions)
            
            # 验证结果
            self.assertTrue(result.success)
            font_results = result.data
            self.assertEqual(len(font_results), 2)
            
            # 验证第一个结果
            self.assertEqual(font_results[0].text, "测试文字1")
            self.assertEqual(font_results[0].matched_font, "思源黑体")
            self.assertEqual(font_results[0].confidence, 0.85)
            self.assertTrue(font_results[0].supports_japanese)
            
    def test_process_regions_with_fallback_font(self):
        """测试使用兜底字体的情况"""
        test_image = np.zeros((200, 300, 3), dtype=np.uint8)
        regions = [self.create_test_region(1, 10, 10, 100, 30, "测试文字")]
        
        with patch.object(self.font_processor, '_match_font') as mock_match_font, \
             patch.object(self.font_processor, '_test_japanese_support') as mock_jp_support:
            
            mock_match_font.return_value = ('台北黑体', 0.75)
            # 第一次调用返回False（不支持日文），第二次返回True（兜底字体支持）
            mock_jp_support.side_effect = [False, True]
            
            result = self.font_processor.process_regions(test_image, regions)
            
            # 验证使用了兜底字体
            self.assertTrue(result.success)
            font_results = result.data
            self.assertEqual(len(font_results), 1)
            self.assertEqual(font_results[0].matched_font, "思源黑体")  # 兜底字体
            
    def test_match_font_with_empty_mapping(self):
        """测试空字体映射的情况"""
        # 临时清空字体映射
        original_mapping = self.font_processor.font_mapping
        self.font_processor.font_mapping = {}
        
        try:
            test_region = np.zeros((30, 100, 3), dtype=np.uint8)
            font_name, similarity = self.font_processor._match_font(test_region, "测试")
            
            self.assertEqual(font_name, "默认字体")
            self.assertEqual(similarity, 0.0)
        finally:
            # 恢复字体映射
            self.font_processor.font_mapping = original_mapping
            
    def test_preprocess_text_region(self):
        """测试文字区域预处理"""
        # 创建彩色测试图像
        color_region = np.random.randint(0, 255, (50, 100, 3), dtype=np.uint8)
        
        processed = self.font_processor._preprocess_text_region(color_region)
        
        # 验证输出是二值化的灰度图
        self.assertEqual(len(processed.shape), 2)  # 灰度图
        self.assertEqual(processed.shape, (64, 64))  # 调整到指定大小
        self.assertTrue(np.all((processed == 0) | (processed == 255)))  # 二值化
        
        # 测试灰度图输入
        gray_region = np.random.randint(0, 255, (40, 80), dtype=np.uint8)
        processed = self.font_processor._preprocess_text_region(gray_region)
        
        self.assertEqual(len(processed.shape), 2)
        self.assertEqual(processed.shape, (64, 64))
        
    @patch('processors.font_processor.ImageFont.truetype')
    @patch('processors.font_processor.ImageDraw.Draw')
    @patch('processors.font_processor.Image.new')
    def test_generate_font_sample(self, mock_image_new, mock_draw, mock_truetype):
        """测试字体样本生成"""
        # 模拟PIL对象
        mock_img = Mock()
        mock_image_new.return_value = mock_img
        mock_draw_obj = Mock()
        mock_draw.return_value = mock_draw_obj
        mock_font = Mock()
        mock_truetype.return_value = mock_font
        
        # 模拟textbbox返回值
        mock_draw_obj.textbbox.return_value = (0, 0, 30, 20)
        
        # 模拟numpy数组转换
        mock_img.__array__ = Mock(return_value=np.zeros((64, 64), dtype=np.uint8))
        
        font_path = os.path.join(self.temp_dir, 'test_font.ttf')
        sample = self.font_processor._generate_font_sample("测试", font_path)
        
        # 验证调用
        mock_truetype.assert_called_once_with(font_path, 32)
        mock_draw_obj.text.assert_called_once()
        
        # 验证返回值
        self.assertIsNotNone(sample)
        self.assertEqual(sample.shape, (64, 64))
        
    def test_generate_font_sample_error_handling(self):
        """测试字体样本生成错误处理"""
        nonexistent_font = "/path/to/nonexistent/font.ttf"
        
        sample = self.font_processor._generate_font_sample("测试", nonexistent_font)
        
        # 应该返回None
        self.assertIsNone(sample)
        
    def test_calculate_similarity(self):
        """测试相似度计算"""
        # 创建两个相同的图像
        region1 = np.zeros((64, 64), dtype=np.uint8)
        region2 = np.zeros((64, 64), dtype=np.uint8)
        
        similarity = self.font_processor._calculate_similarity(region1, region2)
        
        # 相同图像的相似度应该很高
        self.assertGreaterEqual(similarity, 0.9)
        
        # 创建两个不同的图像
        region3 = np.ones((64, 64), dtype=np.uint8) * 255
        
        similarity = self.font_processor._calculate_similarity(region1, region3)
        
        # 不同图像的相似度应该较低
        self.assertLess(similarity, 0.5)
        
    def test_calculate_similarity_different_sizes(self):
        """测试不同尺寸图像的相似度计算"""
        region1 = np.zeros((64, 64), dtype=np.uint8)
        region2 = np.zeros((32, 32), dtype=np.uint8)
        
        # 应该能处理不同尺寸的图像
        similarity = self.font_processor._calculate_similarity(region1, region2)
        
        self.assertIsInstance(similarity, float)
        self.assertGreaterEqual(similarity, 0.0)
        self.assertLessEqual(similarity, 1.0)
        
    @patch('processors.font_processor.ImageFont.truetype')
    @patch('processors.font_processor.ImageDraw.Draw')
    @patch('processors.font_processor.Image.new')
    def test_japanese_support_detection(self, mock_image_new, mock_draw, mock_truetype):
        """测试日文支持检测"""
        # 模拟支持日文的字体
        mock_font = Mock()
        mock_truetype.return_value = mock_font
        mock_draw_obj = Mock()
        mock_draw.return_value = mock_draw_obj
        mock_img = Mock()
        mock_image_new.return_value = mock_img
        
        # 模拟textbbox返回有效边界框（支持日文）
        mock_draw_obj.textbbox.return_value = (0, 0, 20, 20)
        
        font_path = os.path.join(self.temp_dir, 'japanese_font.ttf')
        supports_japanese = self.font_processor._test_japanese_support(font_path)
        
        self.assertTrue(supports_japanese)
        
        # 模拟不支持日文的字体（边界框无效）
        mock_draw_obj.textbbox.return_value = (0, 0, 0, 0)
        
        supports_japanese = self.font_processor._test_japanese_support(font_path)
        
        self.assertFalse(supports_japanese)
        
    def test_japanese_support_detection_error(self):
        """测试日文支持检测错误处理"""
        nonexistent_font = "/path/to/nonexistent/font.ttf"
        
        supports_japanese = self.font_processor._test_japanese_support(nonexistent_font)
        
        self.assertFalse(supports_japanese)
        
    def test_get_font_path(self):
        """测试获取字体路径"""
        # 测试存在的字体
        font_path = self.font_processor.get_font_path("思源黑体")
        expected_path = os.path.join(self.temp_dir, 'test_font1.otf')
        self.assertEqual(font_path, expected_path)
        
        # 测试不存在的字体
        font_path = self.font_processor.get_font_path("不存在的字体")
        self.assertEqual(font_path, "")
        
    def test_process_regions_empty_list(self):
        """测试处理空区域列表"""
        test_image = np.zeros((200, 300, 3), dtype=np.uint8)
        regions = []
        
        result = self.font_processor.process_regions(test_image, regions)
        
        self.assertTrue(result.success)
        self.assertEqual(len(result.data), 0)
        
    def test_process_regions_non_chinese(self):
        """测试处理非中文区域"""
        test_image = np.zeros((200, 300, 3), dtype=np.uint8)
        
        # 创建非中文区域
        poly = np.array([[10, 10], [110, 10], [110, 40], [10, 40]])
        english_region = TextRegion.from_ocr_result(1, poly, "English", 0.9, False)
        regions = [english_region]
        
        result = self.font_processor.process_regions(test_image, regions)
        
        # 应该跳过非中文区域
        self.assertTrue(result.success)
        self.assertEqual(len(result.data), 0)


if __name__ == '__main__':
    unittest.main()
