"""
数据模型测试
"""
import unittest
import numpy as np
from models.data_models import (
    TextRegion, OCRResult, LayoutResult, FontMatchResult, 
    StyleInfo, TranslationResult, RenderConfig, ProcessingResult,
    PipelineConfig
)


class TestDataModels(unittest.TestCase):
    """数据模型测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.sample_poly = np.array([[10, 10], [100, 10], [100, 50], [10, 50]])
        
    def test_text_region_creation(self):
        """测试TextRegion创建"""
        region = TextRegion.from_ocr_result(
            region_id=1,
            poly=self.sample_poly,
            text="测试文字",
            score=0.95,
            is_chinese=True
        )
        
        self.assertEqual(region.id, 1)
        self.assertEqual(region.text, "测试文字")
        self.assertEqual(region.score, 0.95)
        self.assertTrue(region.is_chinese)
        self.assertEqual(region.bbox, (10, 10, 91, 41))
        self.assertEqual(region.center, (55, 30))
        
    def test_ocr_result_properties(self):
        """测试OCRResult属性"""
        chinese_region = TextRegion.from_ocr_result(
            1, self.sample_poly, "中文", 0.9, True
        )
        english_region = TextRegion.from_ocr_result(
            2, self.sample_poly, "English", 0.8, False
        )
        
        ocr_result = OCRResult(
            dt_polys=[self.sample_poly, self.sample_poly],
            rec_texts=["中文", "English"],
            rec_scores=[0.9, 0.8],
            chinese_regions=[chinese_region],
            other_regions=[english_region]
        )
        
        self.assertEqual(ocr_result.total_regions, 2)
        self.assertEqual(ocr_result.chinese_count, 1)
        
    def test_font_match_result(self):
        """测试FontMatchResult"""
        font_result = FontMatchResult(
            text="测试",
            matched_font="思源黑体",
            font_path="/path/to/font.otf",
            confidence=0.85,
            supports_japanese=True,
            region_id=1
        )
        
        self.assertEqual(font_result.text, "测试")
        self.assertEqual(font_result.matched_font, "思源黑体")
        self.assertTrue(font_result.supports_japanese)
        
    def test_style_info(self):
        """测试StyleInfo"""
        style = StyleInfo(
            estimated_font_size=24,
            color=(0, 0, 0),
            background_color=(255, 255, 255),
            is_bold=False,
            contrast_ratio=21.0
        )
        
        self.assertEqual(style.estimated_font_size, 24)
        self.assertEqual(style.color, (0, 0, 0))
        self.assertFalse(style.is_bold)
        
    def test_processing_result(self):
        """测试ProcessingResult"""
        # 测试成功结果
        success_result = ProcessingResult.success_result("test_data")
        self.assertTrue(success_result.success)
        self.assertEqual(success_result.data, "test_data")
        self.assertEqual(success_result.error_message, "")
        
        # 测试错误结果
        error_result = ProcessingResult.error_result("test_error")
        self.assertFalse(error_result.success)
        self.assertEqual(error_result.error_message, "test_error")
        self.assertIsNone(error_result.data)
        
    def test_pipeline_config(self):
        """测试PipelineConfig"""
        config = PipelineConfig(
            ocr_confidence_threshold=0.7,
            alignment_threshold=10,
            default_font_weight=600
        )
        
        self.assertEqual(config.ocr_confidence_threshold, 0.7)
        self.assertEqual(config.alignment_threshold, 10)
        self.assertEqual(config.default_font_weight, 600)
        self.assertEqual(config.font_size_range, (12, 72))  # 默认值
        
    def test_layout_result(self):
        """测试LayoutResult"""
        regions = [{'id': 1, 'text': '测试', 'bbox': (10, 10, 50, 20)}]
        h_align = {'type': 'left', 'left_groups': [], 'center_groups': [], 'right_groups': []}
        v_dist = {'type': 'horizontal', 'rows': 1, 'columns': 2}
        strategies = [{'type': 'left_alignment', 'description': '左对齐'}]
        
        layout_result = LayoutResult(
            layout_mode='horizontal_left_aligned',
            regions=regions,
            horizontal_alignment=h_align,
            vertical_distribution=v_dist,
            alignment_strategies=strategies
        )
        
        self.assertEqual(layout_result.layout_mode, 'horizontal_left_aligned')
        self.assertEqual(len(layout_result.regions), 1)
        self.assertEqual(layout_result.horizontal_alignment['type'], 'left')
        
    def test_translation_result(self):
        """测试TranslationResult"""
        font_info = FontMatchResult(
            text="测试", matched_font="思源黑体", font_path="/path",
            confidence=0.8, supports_japanese=True, region_id=1
        )
        style_info = StyleInfo(24, (0, 0, 0), (255, 255, 255), False, 21.0)
        
        translation_result = TranslationResult(
            original_text="测试",
            translated_text="テスト",
            bbox=(10, 10, 50, 20),
            style_info=style_info,
            font_info=font_info,
            group_key="group1",
            group_scale_factor=1.0
        )
        
        self.assertEqual(translation_result.original_text, "测试")
        self.assertEqual(translation_result.translated_text, "テスト")
        self.assertEqual(translation_result.group_key, "group1")
        self.assertIsNone(translation_result.text_x)  # 可选属性
        
    def test_render_config(self):
        """测试RenderConfig"""
        render_config = RenderConfig(
            text_x=100,
            text_y=50,
            text_width=200,
            text_height=30,
            font_size=24,
            font_path="/path/to/font.otf",
            alignment_type="center",
            color=(0, 0, 0)
        )
        
        self.assertEqual(render_config.text_x, 100)
        self.assertEqual(render_config.alignment_type, "center")
        self.assertEqual(render_config.color, (0, 0, 0))


if __name__ == '__main__':
    unittest.main()
