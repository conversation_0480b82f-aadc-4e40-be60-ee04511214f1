"""
布局分析处理器
负责文本布局模式识别和对齐策略分析
"""
import cv2
import os
import json
import numpy as np
from typing import List, Dict, Any, Tuple
from collections import defaultdict
import math
from PIL import Image, ImageDraw, ImageFont

from models.data_models import TextRegion, LayoutResult, ProcessingResult
from config.settings import get_config_manager


class LayoutProcessor:
    """布局分析处理器"""

    # 调试颜色常量 - 重新设计颜色方案
    DEBUG_COLORS = {
        # 对齐类型 - 使用高对比度颜色
        'left_aligned': (46, 125, 50),        # 深绿色 - 左对齐
        'center_aligned': (25, 118, 210),     # 深蓝色 - 居中对齐
        'right_aligned': (211, 47, 47),       # 深红色 - 右对齐
        'distribution': (123, 31, 162),       # 深紫色 - 分布对齐
        
        # 样式分组 - 使用明亮的区分色
        'style_group_1': (255, 87, 34),       # 橙红色
        'style_group_2': (76, 175, 80),       # 绿色
        'style_group_3': (33, 150, 243),      # 蓝色
        'style_group_4': (255, 193, 7),       # 黄色
        'style_group_5': (156, 39, 176),      # 紫色
        'style_group_6': (0, 188, 212),       # 青色
        'style_group_7': (255, 111, 97),      # 粉红色
        'style_group_8': (139, 195, 74),      # 浅绿色
        
        # 辅助元素
        'ungrouped': (158, 158, 158),          # 灰色 - 未分组
        'alignment_line': (255, 235, 59),      # 亮黄色 - 对齐线
        'connection_line': (96, 125, 139),     # 蓝灰色 - 连接线
        'region_border': (224, 224, 224),      # 浅灰色 - 区域边框
        'grid_line': (189, 189, 189),          # 网格线
        'background': (248, 249, 250),         # 背景色
        'text_primary': (33, 33, 33),          # 主要文字
        'text_secondary': (117, 117, 117),     # 次要文字
    }

    def __init__(self):
        """初始化布局分析处理器"""
        self.config_manager = get_config_manager()
        self.alignment_threshold = self.config_manager.config.alignment_threshold
        self.proximity_threshold = self.config_manager.config.proximity_threshold
        print(f"布局分析处理器初始化完成 (对齐阈值: {self.alignment_threshold}px, 邻近阈值: {self.proximity_threshold}px)")
    
    def analyze_layout(self, chinese_regions: List[TextRegion], font_results: List = None) -> ProcessingResult:
        """分析文本布局"""
        try:
            print(f"布局分析: {len(chinese_regions)} 个中文区域")

            if len(chinese_regions) < 2:
                layout_result = self._create_simple_layout_result(chinese_regions)
            else:
                # 新的统一分组逻辑：直接基于样式相似性进行分组
                layout_result = self._analyze_layout_with_style_grouping(chinese_regions, font_results)

            self._print_layout_summary(layout_result)

            # 生成调试图像和数据（如果启用）
            if self.config_manager.config.enable_layout_debug:
                self._generate_debug_outputs(chinese_regions, layout_result)

            return ProcessingResult.success_result(layout_result)

        except Exception as e:
            error_msg = f"布局分析失败: {str(e)}"
            print(error_msg)
            return ProcessingResult.error_result(error_msg)

    def _analyze_layout_with_style_grouping(self, chinese_regions: List[TextRegion], font_results: List = None) -> LayoutResult:
        """新的统一分组逻辑：直接基于样式相似性进行分组"""
        print("开始样式相似性分组...")
        
        # 转换为内部数据格式
        regions_data = self._convert_regions_to_dict(chinese_regions)
        
        # 构建区域ID到TextRegion的映射
        region_map = {region.id: region for region in chinese_regions}
        
        # 核心：基于样式相似性直接分组
        style_groups = self._group_regions_by_style_similarity(regions_data, region_map, font_results)
        
        # 分析垂直分布和空间模式（保留原有逻辑）
        vertical_distribution = self._analyze_vertical_distribution(regions_data)
        spacing_pattern = self._analyze_spacing_pattern(regions_data)
        
        # 确定布局模式
        layout_mode = self._identify_layout_mode_from_style_groups(style_groups, vertical_distribution, spacing_pattern)
        
        # 生成对齐策略
        alignment_strategies = self._generate_alignment_strategies_from_style_groups(style_groups)
        
        # 构建水平对齐结果（兼容现有结构）
        horizontal_alignment = self._build_horizontal_alignment_from_style_groups(style_groups)
        
        # 直接统一样式
        self._unify_style_groups(style_groups, region_map)
        
        return LayoutResult(
            layout_mode=layout_mode,
            regions=regions_data,
            horizontal_alignment=horizontal_alignment,
            vertical_distribution=vertical_distribution,
            alignment_strategies=alignment_strategies
        )


    
    def _convert_regions_to_dict(self, regions: List[TextRegion]) -> List[Dict[str, Any]]:
        """将TextRegion转换为字典格式"""
        regions_data = []
        for region in regions:
            x, y, w, h = region.bbox
            regions_data.append({
                'id': region.id,
                'text': region.text,
                'bbox': region.bbox,
                'center': region.center,
                'left': x,
                'right': x + w,
                'top': y,
                'bottom': y + h
            })
        return regions_data
    
    def _group_regions_by_style_similarity(self, regions_data: List[Dict], region_map: Dict[int, TextRegion], font_results: List = None) -> List[List[Dict]]:
        """基于样式相似性对区域进行分组"""
        print("  分析样式相似性...")
        
        if len(regions_data) < 2:
            return [regions_data] if regions_data else []
        
        # 使用并查集算法进行分组
        groups = []
        remaining_regions = regions_data.copy()
        
        while remaining_regions:
            # 取第一个区域作为种子
            seed_region = remaining_regions.pop(0)
            current_group = [seed_region]
            
            # 找出所有与种子区域样式相似的区域
            i = 0
            while i < len(remaining_regions):
                candidate_region = remaining_regions[i]
                
                # 检查样式相似性
                if self._are_regions_style_similar(seed_region, candidate_region, region_map, font_results):
                    current_group.append(candidate_region)
                    remaining_regions.pop(i)
                else:
                    i += 1
            
            groups.append(current_group)
        
        # 打印分组结果
        for i, group in enumerate(groups):
            group_texts = [region['text'] for region in group]
            print(f"  样式组{i+1}: {group_texts} ({len(group)}个区域)")
        
        return groups
    
    def _are_regions_style_similar(self, region1: Dict, region2: Dict, region_map: Dict[int, TextRegion], font_results: List = None) -> bool:
        """判断两个区域的样式是否相似"""
        text_region1 = region_map.get(region1['id'])
        text_region2 = region_map.get(region2['id'])
        
        if not text_region1 or not text_region2:
            return False
        
        # 相似性评分系统（调整权重：高度和位置为主，字体颜色为辅）
        similarity_score = 0
        max_score = 0
        
        # 1. 高度相似性检查（权重最高：4分）
        max_score += 4
        if text_region1.style_info and text_region2.style_info:
            height1 = text_region1.style_info.get('precise_height', 0)
            height2 = text_region2.style_info.get('precise_height', 0)
            if height1 and height2:
                height_diff = abs(height1 - height2)
                if height_diff <= 1:  # 完全一致（已做高度统一）
                    similarity_score += 4
                elif height_diff <= 3:  # 非常接近
                    similarity_score += 3
                elif height_diff <= 6:  # 比较接近
                    similarity_score += 2
                elif height_diff <= 10:  # 还算接近
                    similarity_score += 1
        
        # 2. 位置关系检查（权重提高：3分）
        max_score += 3
        position_similarity = self._check_position_relationship_score(region1, region2)
        similarity_score += position_similarity
        
        # 3. 语义相关性检查（权重提高：2分）
        max_score += 2
        semantic_similarity = self._check_semantic_relationship_score(region1, region2)
        similarity_score += semantic_similarity
        
        # 4. 字体相似性检查（权重降低：1分，识别准确度低）
        max_score += 1
        font_similarity = self._check_font_similarity_score(region1, region2, region_map, font_results)
        similarity_score += font_similarity
        
        # 5. 颜色相似性检查（权重降低：1分，识别准确度低）
        max_score += 1
        color_similarity = self._check_color_similarity_score(region1, region2, region_map)
        similarity_score += color_similarity
        
        # 相似性阈值：需要达到总分的55%以上（降低阈值，因为字体颜色权重降低）
        similarity_threshold = max_score * 0.55
        is_similar = similarity_score >= similarity_threshold
        
        if is_similar:
            print(f"    '{region1['text']}' 与 '{region2['text']}' 样式相似 (得分: {similarity_score}/{max_score})")
        
        return is_similar
    
    def _check_font_similarity_score(self, region1: Dict, region2: Dict, region_map: Dict[int, TextRegion], font_results: List = None) -> int:
        """检查字体相似性并返回得分 (0-2分)"""
        if font_results:
            font_map = {result.region_id: result.matched_font for result in font_results}
            font1 = font_map.get(region1['id'])
            font2 = font_map.get(region2['id'])
        else:
            text_region1 = region_map.get(region1['id'])
            text_region2 = region_map.get(region2['id'])
            font1 = text_region1.style_info.get('matched_font') if text_region1 and text_region1.style_info else None
            font2 = text_region2.style_info.get('matched_font') if text_region2 and text_region2.style_info else None
        
        if not font1 or not font2:
            return 0
        
        # 完全相同的字体
        if font1 == font2:
            return 2
        
        # 字体系列相似（如思源黑体 vs Noto Sans）
        simple_font1 = self._simplify_font_name(font1)
        simple_font2 = self._simplify_font_name(font2)
        if simple_font1 == simple_font2:
            return 1
        
        # 都是常见字体系列
        common_fonts = {'Noto Sans', '思源黑体', 'Source Han', '微软雅黑'}
        if (any(cf in font1 for cf in common_fonts) and 
            any(cf in font2 for cf in common_fonts)):
            return 1
        
        return 0
    
    def _check_color_similarity_score(self, region1: Dict, region2: Dict, region_map: Dict[int, TextRegion]) -> int:
        """检查颜色相似性并返回得分 (0-2分)"""
        text_region1 = region_map.get(region1['id'])
        text_region2 = region_map.get(region2['id'])
        
        if (not text_region1 or not text_region1.style_info or 
            not text_region2 or not text_region2.style_info):
            return 0
        
        color1 = text_region1.style_info.get('color') or text_region1.style_info.get('text_color')
        color2 = text_region2.style_info.get('color') or text_region2.style_info.get('text_color')
        
        if not color1 or not color2:
            return 0
        
        # 计算颜色距离
        color_distance = self._color_distance(color1, color2)
        
        if color_distance <= 15:  # 非常相似
            return 2
        elif color_distance <= 30:  # 比较相似
            return 1
        else:
            return 0
    
    def _check_position_relationship_score(self, region1: Dict, region2: Dict) -> int:
        """检查位置关系并返回得分 (0-3分)"""
        score = 0
        
        # 水平相邻检查
        horizontal_gap = min(
            abs(region1['right'] - region2['left']),
            abs(region2['right'] - region1['left'])
        )
        
        # 垂直相邻检查
        vertical_gap = min(
            abs(region1['bottom'] - region2['top']),
            abs(region2['bottom'] - region1['top'])
        )
        
        # 水平对齐检查（Y坐标重叠）
        y_overlap = max(0, min(region1['bottom'], region2['bottom']) - max(region1['top'], region2['top']))
        vertical_alignment = y_overlap > 0
        
        # 垂直对齐检查（X坐标重叠）
        x_overlap = max(0, min(region1['right'], region2['right']) - max(region1['left'], region2['left']))
        horizontal_alignment = x_overlap > 0
        
        # 左边界对齐检查
        left_diff = abs(region1['left'] - region2['left'])
        left_aligned = left_diff <= 20
        
        # 垂直间距检查（上下排列）
        if horizontal_alignment and vertical_gap <= 150:  # 垂直相邻且水平有重叠
            if vertical_gap <= 50:  # 非常接近
                score += 2
            else:  # 比较接近
                score += 1
        
        # 水平间距检查（左右排列）
        if vertical_alignment and horizontal_gap <= 300:  # 水平相邻且垂直有重叠
            if horizontal_gap <= 100:  # 非常接近
                score += 1
            else:  # 比较接近
                score += 1
        
        # 左对齐加分（同一列）
        if left_aligned:
            score += 1
        
        return min(score, 3)  # 最高3分
    
    def _check_semantic_relationship_score(self, region1: Dict, region2: Dict) -> int:
        """检查语义相关性并返回得分 (0-2分)"""
        text1 = region1['text']
        text2 = region2['text']
        score = 0
        
        # 检查是否都包含步骤序号（最高分）
        import re
        step_pattern = r'^[0-9]+[\.、]'
        has_step1 = bool(re.match(step_pattern, text1))
        has_step2 = bool(re.match(step_pattern, text2))
        
        if has_step1 and has_step2:
            score += 2  # 步骤序号是强语义相关性
        
        # 检查相关关键词（中等分）
        related_keywords = [
            ['安装', '使用', '步骤', '方法', '操作'],
            ['价格', '费用', '成本', '税', '报价', '税点'],
            ['产品', '商品', '服务', '详情'],
            ['联系', '客服', '购买', '采购', '定制'],
            ['店铺', '通知', '说明', '谅解']
        ]
        
        for keyword_group in related_keywords:
            has_keyword1 = any(kw in text1 for kw in keyword_group)
            has_keyword2 = any(kw in text2 for kw in keyword_group)
            if has_keyword1 and has_keyword2:
                score += 1
                break  # 只计算第一个匹配的关键词组
        
        return min(score, 2)  # 最高2分
    
    def _build_horizontal_alignment_from_style_groups(self, style_groups: List[List[Dict]]) -> Dict[str, Any]:
        """使用两级分组策略：先基于位置检测对齐，再基于样式智能细分"""
        # 扁平化所有区域用于位置分析
        all_regions = []
        for group in style_groups:
            all_regions.extend(group)
        
        if len(all_regions) < 2:
            return {'type': 'single', 'left_groups': [], 'center_groups': [], 'right_groups': [], 'distribution_groups': style_groups}
        
        # 第一级：基于位置的对齐检测
        position_based_alignment = self._detect_position_based_alignment(all_regions)
        
        # 第二级：在每个对齐组内基于样式智能细分
        refined_left_groups = self._refine_alignment_groups_by_style(position_based_alignment['left_groups'])
        refined_center_groups = self._refine_alignment_groups_by_style(position_based_alignment['center_groups'])
        refined_right_groups = self._refine_alignment_groups_by_style(position_based_alignment['right_groups'])
        
        # 未对齐的区域保持原样式分组
        unaligned_regions = position_based_alignment['unaligned_regions']
        distribution_groups = self._group_unaligned_regions_by_style(unaligned_regions, style_groups)
        
        # 确定主要对齐类型
        primary_type = self._determine_primary_alignment_type(refined_left_groups, refined_center_groups, refined_right_groups)
        
        return {
            'type': primary_type,
            'left_groups': refined_left_groups,
            'center_groups': refined_center_groups,
            'right_groups': refined_right_groups,
            'distribution_groups': distribution_groups
        }
    
    def _detect_position_based_alignment(self, regions: List[Dict]) -> Dict[str, Any]:
        """第一级：基于位置检测对齐"""
        left_aligned = []
        center_aligned = []
        right_aligned = []
        unaligned = []
        
        # 收集所有位置信息
        left_positions = {}
        center_positions = {}
        right_positions = {}
        
        for region in regions:
            left_pos = region['left']
            center_pos = region['center'][0]
            right_pos = region['right']
            
            # 按位置分组（使用较大的容差来捕获更多对齐关系）
            alignment_threshold = 15  # 放宽对齐阈值
            
            # 检查左对齐
            found_left_group = False
            for pos in left_positions:
                if abs(left_pos - pos) <= alignment_threshold:
                    left_positions[pos].append(region)
                    found_left_group = True
                    break
            if not found_left_group:
                left_positions[left_pos] = [region]
            
            # 检查中心对齐
            found_center_group = False
            for pos in center_positions:
                if abs(center_pos - pos) <= alignment_threshold:
                    center_positions[pos].append(region)
                    found_center_group = True
                    break
            if not found_center_group:
                center_positions[center_pos] = [region]
            
            # 检查右对齐
            found_right_group = False
            for pos in right_positions:
                if abs(right_pos - pos) <= alignment_threshold:
                    right_positions[pos].append(region)
                    found_right_group = True
                    break
            if not found_right_group:
                right_positions[right_pos] = [region]
        
        # 提取有效的对齐组（至少2个区域）
        left_groups = [group for group in left_positions.values() if len(group) >= 2]
        center_groups = [group for group in center_positions.values() if len(group) >= 2]
        right_groups = [group for group in right_positions.values() if len(group) >= 2]
        
        # 找出未对齐的区域
        aligned_region_ids = set()
        for group in left_groups + center_groups + right_groups:
            for region in group:
                aligned_region_ids.add(region['id'])
        
        unaligned_regions = [region for region in regions if region['id'] not in aligned_region_ids]
        
        print(f"位置对齐检测结果: 左对齐{len(left_groups)}组, 中心对齐{len(center_groups)}组, 右对齐{len(right_groups)}组, 未对齐{len(unaligned_regions)}个")
        
        return {
            'left_groups': left_groups,
            'center_groups': center_groups,
            'right_groups': right_groups,
            'unaligned_regions': unaligned_regions
        }
    
    def _refine_alignment_groups_by_style(self, alignment_groups: List[List[Dict]]) -> List[List[Dict]]:
        """第二级：在对齐组内基于样式智能细分"""
        refined_groups = []
        
        for group in alignment_groups:
            if len(group) <= 2:
                # 小组直接保留
                refined_groups.append(group)
                continue
            
            # 基于样式相似性细分大组
            style_subgroups = self._subdivide_by_style_similarity(group)
            refined_groups.extend(style_subgroups)
        
        return refined_groups
    
    def _subdivide_by_style_similarity(self, regions: List[Dict]) -> List[List[Dict]]:
        """基于样式相似性细分区域组"""
        if len(regions) <= 2:
            return [regions]
        
        # 收集样式信息
        style_info = []
        for region in regions:
            info = {
                'region': region,
                'height': region['bottom'] - region['top'],  # 使用bbox高度
                'text': region['text']
            }
            style_info.append(info)
        
        # 基于高度分组（主要因素）
        height_groups = self._group_by_height_similarity(style_info)
        
        # 在每个高度组内基于颜色进一步细分
        final_groups = []
        for height_group in height_groups:
            if len(height_group) <= 2:
                final_groups.append([item['region'] for item in height_group])
        else:
                color_subgroups = self._group_by_color_similarity(height_group)
                final_groups.extend([[item['region'] for item in subgroup] for subgroup in color_subgroups])
        
        return final_groups
    
    def _group_by_height_similarity(self, style_info: List[Dict]) -> List[List[Dict]]:
        """基于高度相似性分组"""
        if not style_info:
            return []
        
        # 按高度排序
        sorted_info = sorted(style_info, key=lambda x: x['height'])
        
        groups = []
        current_group = [sorted_info[0]]
        
        for i in range(1, len(sorted_info)):
            current_height = sorted_info[i]['height']
            last_height = current_group[-1]['height']
            
            # 高度相似性阈值（允许一定误差）
            height_threshold = max(3, last_height * 0.15)  # 15%的相对误差或至少3px
            
            if abs(current_height - last_height) <= height_threshold:
                current_group.append(sorted_info[i])
        else:
                groups.append(current_group)
                current_group = [sorted_info[i]]
        
        groups.append(current_group)
        
        # 格式化输出分组信息
        group_info = []
        for g in groups:
            height = g[0]['height']
            group_info.append(f'{len(g)}个区域(高度{height}px)')
        print(f"    高度分组: {group_info}")
        return groups
    
    def _group_by_color_similarity(self, style_info: List[Dict]) -> List[List[Dict]]:
        """基于颜色相似性分组（需要从region_map获取颜色信息）"""
        # 由于当前无法直接获取颜色信息，暂时基于文本内容的语义相似性分组
        # 这里可以根据文本长度、内容类型等进行简单分组
        
        if len(style_info) <= 2:
            return [style_info]
        
        # 简单的基于文本长度的分组策略
        short_texts = []  # 短文本（如标签、数字）
        long_texts = []   # 长文本（如描述）
        
        for info in style_info:
            text_length = len(info['text'])
            if text_length <= 5:  # 短文本
                short_texts.append(info)
            else:  # 长文本
                long_texts.append(info)
        
        groups = []
        if short_texts:
            groups.append(short_texts)
        if long_texts:
            groups.append(long_texts)
        
        print(f"      颜色/语义分组: {[f'{len(g)}个区域' for g in groups]}")
        return groups if groups else [style_info]
    
    def _group_unaligned_regions_by_style(self, unaligned_regions: List[Dict], original_style_groups: List[List[Dict]]) -> List[List[Dict]]:
        """将未对齐的区域按原样式分组归类"""
        if not unaligned_regions:
            return []
        
        # 找出包含未对齐区域的原始样式组
        unaligned_style_groups = []
        unaligned_ids = {region['id'] for region in unaligned_regions}
        
        for style_group in original_style_groups:
            group_ids = {region['id'] for region in style_group}
            # 如果样式组中有未对齐的区域，保留该组
            if group_ids.intersection(unaligned_ids):
                unaligned_style_groups.append(style_group)
        
        return unaligned_style_groups
    
    def _determine_primary_alignment_type(self, left_groups: List, center_groups: List, right_groups: List) -> str:
        """确定主要对齐类型"""
        left_count = sum(len(group) for group in left_groups)
        center_count = sum(len(group) for group in center_groups)
        right_count = sum(len(group) for group in right_groups)
        
        if left_count > center_count and left_count > right_count:
            return 'left'
        elif right_count > center_count and right_count > left_count:
            return 'right'
        elif center_count > 0:
            return 'center'
        else:
            return 'mixed'
    
    def _unify_style_groups(self, style_groups: List[List[Dict]], region_map: Dict[int, TextRegion]):
        """统一每个样式分组的显示参数"""
        print("\n开始统一样式分组...")
        
        for group_idx, group in enumerate(style_groups):
            if len(group) <= 1:
                continue  # 单个区域无需统一
            
            print(f"\n统一样式组{group_idx+1}:")
            
            # 收集组内所有区域的样式信息
            group_colors = []
            group_heights = []
            group_fonts = []
            region_texts = []
            text_regions = []
            
            for region_dict in group:
                region_id = region_dict.get('id')
                text_region = region_map.get(region_id)
                
                if text_region:
                    text_regions.append(text_region)
                    region_texts.append(text_region.text)
                    
                    if text_region.style_info:
                        style = text_region.style_info
                        if 'color' in style or 'text_color' in style:
                            color = style.get('color') or style.get('text_color')
                            group_colors.append(color)
                        if 'precise_height' in style:
                            group_heights.append(style['precise_height'])
                        if 'matched_font' in style:
                            group_fonts.append(style['matched_font'])
            
            if not text_regions:
                continue
            
            print(f"  分组内容: {region_texts}")
            
            # 1. 统一颜色：选择最常见的颜色
            unified_color = self._choose_unified_color(group_colors) if group_colors else (0, 0, 0)
            print(f"  统一颜色: {unified_color}")
            
            # 2. 统一高度：使用已经统一的precise_height
            unified_height = group_heights[0] if group_heights else 24
            print(f"  统一高度: {unified_height}px")
            
            # 3. 统一字体：基于频次+置信度智能选择
            unified_font = self._choose_unified_font(group_fonts, text_regions) if group_fonts else "思源黑体"
            print(f"  统一字体: {unified_font}")
            
            # 直接修改所有TextRegion的属性
            for text_region in text_regions:
                if not text_region.style_info:
                    text_region.style_info = {}
                
                # 直接覆盖样式信息
                text_region.style_info['color'] = unified_color
                text_region.style_info['text_color'] = unified_color
                text_region.style_info['precise_height'] = unified_height
                text_region.style_info['matched_font'] = unified_font
                text_region.style_info['is_unified'] = True
                text_region.style_info['unified_group_id'] = group_idx + 1
                
                print(f"    '{text_region.text}': 字体={unified_font}, 颜色={unified_color}, 高度={unified_height}px")
        
        print("样式分组统一完成")
    
    def _detect_horizontal_alignment(self, regions: List[Dict]) -> Dict[str, Any]:
        """检测水平对齐模式 - 改进版：先按行分组，再分析行内对齐"""
        if len(regions) < 2:
            return {'type': 'single', 'left_groups': [], 'center_groups': [], 'right_groups': [], 'distribution_groups': []}
        
        # 第一步：按Y坐标将区域分成行
        row_groups = self._group_regions_by_rows(regions)
        
        # 第二步：分析每行内部的对齐关系
        all_left_groups = []
        all_center_groups = []
        all_right_groups = []
        all_distribution_groups = []
        
        for row_regions in row_groups:
            if len(row_regions) < 2:
                continue  # 单个区域的行不需要分析对齐
            
            # 在行内分析严格对齐关系
            row_left_groups = self._find_aligned_groups_in_row(row_regions, 'left')
            row_center_groups = self._find_aligned_groups_in_row(row_regions, 'center')
            row_right_groups = self._find_aligned_groups_in_row(row_regions, 'right')
            
            all_left_groups.extend(row_left_groups)
            all_center_groups.extend(row_center_groups)
            all_right_groups.extend(row_right_groups)
            
            # 如果行内没有找到严格对齐组，将整行作为分布组
            has_alignment = bool(row_left_groups or row_center_groups or row_right_groups)
            if not has_alignment and len(row_regions) >= 2:
                all_distribution_groups.append(row_regions)
        
        # 第三步：跨行检测对齐（原有逻辑）
        cross_row_left_groups = self._find_cross_row_aligned_groups(regions, 'left')
        cross_row_center_groups = self._find_cross_row_aligned_groups(regions, 'center')
        cross_row_right_groups = self._find_cross_row_aligned_groups(regions, 'right')
        
        # 合并行内和跨行的对齐组
        all_left_groups.extend(cross_row_left_groups)
        all_center_groups.extend(cross_row_center_groups)
        all_right_groups.extend(cross_row_right_groups)
        
        # 确定主要对齐模式
        alignment_type = self._determine_primary_alignment(all_left_groups, all_center_groups, all_right_groups)
        
        # 如果没有找到明确的对齐模式，但有分布组，则标记为分布式
        if alignment_type == 'mixed' and all_distribution_groups:
            alignment_type = 'distributed'
        
        return {
            'type': alignment_type,
            'left_groups': all_left_groups,
            'center_groups': all_center_groups,
            'right_groups': all_right_groups,
            'distribution_groups': all_distribution_groups
        }
    
    def _group_regions_by_rows(self, regions: List[Dict]) -> List[List[Dict]]:
        """按Y坐标将区域分成行"""
        if not regions:
            return []
        
        # 按Y坐标排序
        sorted_regions = sorted(regions, key=lambda r: r['top'])
        
        rows = []
        current_row = [sorted_regions[0]]
        
        for i in range(1, len(sorted_regions)):
            current_region = sorted_regions[i]
            last_region = current_row[-1]
            
            # 检查是否在同一行（Y坐标相近）
            y_diff = abs(current_region['top'] - last_region['top'])
            if y_diff <= self.alignment_threshold:
                current_row.append(current_region)
            else:
                rows.append(current_row)
                current_row = [current_region]
        
        rows.append(current_row)
        return rows
    
    def _find_aligned_groups_in_row(self, row_regions: List[Dict], align_type: str) -> List[List[Dict]]:
        """在单行内查找对齐组"""
        if len(row_regions) < 2:
            return []
        
        # 对于行内对齐，我们需要更宽松的判断
        # 因为同一行的文字可能是左右分布，而不是严格对齐
        
        if align_type == 'left':
            # 检查是否有多个区域的左边界接近
            return self._find_aligned_groups(row_regions, 'left')
        elif align_type == 'right':
            # 检查是否有多个区域的右边界接近
            return self._find_aligned_groups(row_regions, 'right')
        elif align_type == 'center':
            # 检查是否有多个区域的中心接近
            return self._find_aligned_groups(row_regions, 'center')
        else:
            return []
    
    def _find_cross_row_aligned_groups(self, regions: List[Dict], align_type: str) -> List[List[Dict]]:
        """查找跨行的对齐组（原有逻辑）"""
        return self._find_aligned_groups(regions, align_type)
    
    def _find_aligned_groups(self, regions: List[Dict], align_type: str) -> List[List[Dict]]:
        """查找对齐组"""
        if align_type == 'left':
            key_func = lambda r: r['left']
        elif align_type == 'right':
            key_func = lambda r: r['right']
        else:  # center
            key_func = lambda r: r['center'][0]
        
        # 按对齐位置分组
        position_groups = defaultdict(list)
        for region in regions:
            pos = key_func(region)
            position_groups[pos].append(region)
        
        # 合并相近位置的组
        aligned_groups = []
        sorted_positions = sorted(position_groups.keys())
        
        current_group = []
        for pos in sorted_positions:
            if not current_group:
                current_group.extend(position_groups[pos])
            else:
                # 检查是否与当前组的位置接近
                last_pos = key_func(current_group[-1])
                if abs(pos - last_pos) <= self.alignment_threshold:
                    current_group.extend(position_groups[pos])
                else:
                    if len(current_group) >= 2:
                        aligned_groups.append(current_group)
                    current_group = list(position_groups[pos])
        
        # 添加最后一组
        if len(current_group) >= 2:
            aligned_groups.append(current_group)
        
        return aligned_groups
    
    def _determine_primary_alignment(self, left_groups, center_groups, right_groups) -> str:
        """确定主要对齐模式"""
        left_count = sum(len(group) for group in left_groups)
        center_count = sum(len(group) for group in center_groups)
        right_count = sum(len(group) for group in right_groups)
        
        if left_count > center_count and left_count > right_count:
            return 'left'
        elif right_count > center_count and right_count > left_count:
            return 'right'
        elif center_count > 0:
            return 'center'
        else:
            return 'mixed'
    
    def _analyze_vertical_distribution(self, regions: List[Dict]) -> Dict[str, Any]:
        """分析垂直分布模式"""
        if len(regions) < 2:
            return {'type': 'single', 'rows': 1, 'columns': 1}
        
        # 按Y坐标分组找行
        y_positions = [r['top'] for r in regions]
        y_groups = self._group_by_proximity(y_positions, self.proximity_threshold)
        rows = len(y_groups)
        
        # 按X坐标分组找列
        x_positions = [r['left'] for r in regions]
        x_groups = self._group_by_proximity(x_positions, self.proximity_threshold)
        columns = len(x_groups)
        
        # 确定分布类型
        if rows == 1 and columns > 1:
            dist_type = 'horizontal'
        elif rows > 1 and columns == 1:
            dist_type = 'vertical'
        elif rows == 2 and columns == 2:
            dist_type = 'grid_2x2'
        elif rows > 2 or columns > 2:
            dist_type = 'complex'
        else:
            dist_type = 'scattered'
        
        return {
            'type': dist_type,
            'rows': rows,
            'columns': columns,
            'row_groups': y_groups,
            'column_groups': x_groups
        }
    
    def _group_by_proximity(self, positions: List[int], threshold: int) -> List[List[int]]:
        """按邻近度分组"""
        if not positions:
            return []
        
        sorted_positions = sorted(set(positions))
        groups = []
        current_group = [sorted_positions[0]]
        
        for i in range(1, len(sorted_positions)):
            if sorted_positions[i] - sorted_positions[i-1] <= threshold:
                current_group.append(sorted_positions[i])
            else:
                groups.append(current_group)
                current_group = [sorted_positions[i]]
        
        groups.append(current_group)
        return groups
    
    def _analyze_spacing_pattern(self, regions: List[Dict]) -> Dict[str, Any]:
        """分析间距模式"""
        if len(regions) < 2:
            return {'type': 'none'}
        
        # 计算水平间距
        h_spacings = []
        for i in range(len(regions)):
            for j in range(i + 1, len(regions)):
                r1, r2 = regions[i], regions[j]
                # 只计算同一行的间距
                if abs(r1['top'] - r2['top']) <= self.alignment_threshold:
                    spacing = abs(r1['center'][0] - r2['center'][0])
                    h_spacings.append(spacing)
        
        # 计算垂直间距
        v_spacings = []
        for i in range(len(regions)):
            for j in range(i + 1, len(regions)):
                r1, r2 = regions[i], regions[j]
                # 只计算同一列的间距
                if abs(r1['center'][0] - r2['center'][0]) <= self.alignment_threshold:
                    spacing = abs(r1['center'][1] - r2['center'][1])
                    v_spacings.append(spacing)
        
        # 分析间距规律性
        h_regularity = self._analyze_spacing_regularity(h_spacings)
        v_regularity = self._analyze_spacing_regularity(v_spacings)
        
        return {
            'type': 'regular' if h_regularity == 'regular' and v_regularity == 'regular' else 'irregular',
            'horizontal_regularity': h_regularity,
            'vertical_regularity': v_regularity,
            'horizontal_spacings': h_spacings,
            'vertical_spacings': v_spacings
        }
    
    def _analyze_spacing_regularity(self, spacings: List[int]) -> str:
        """分析间距规律性"""
        if len(spacings) < 2:
            return 'insufficient_data'
        
        # 计算变异系数
        mean_spacing = np.mean(spacings)
        std_spacing = np.std(spacings)
        
        if mean_spacing == 0:
            return 'zero_spacing'
        
        cv = std_spacing / mean_spacing
        
        if cv < 0.2:
            return 'regular'
        elif cv < 0.5:
            return 'semi_regular'
        else:
            return 'irregular'

    def _identify_layout_mode_from_style_groups(self, style_groups: List[List[Dict]], vertical_distribution: Dict, spacing_pattern: Dict) -> str:
        """从样式分组确定布局模式"""
        total_groups = len(style_groups)
        total_regions = sum(len(group) for group in style_groups)
        
        if total_regions <= 1:
            return 'single_text'
        elif total_groups == 1:
            return 'unified_style'
        elif total_groups <= 3:
            return 'multi_style_simple'
        else:
            return 'multi_style_complex'

    def _generate_alignment_strategies_from_style_groups(self, style_groups: List[List[Dict]]) -> List[Dict[str, Any]]:
        """从样式分组生成对齐策略"""
        strategies = []

        for i, group in enumerate(style_groups):
            alignment_type = self._analyze_group_alignment(group)
            
            strategy = {
                'type': f'{alignment_type}_alignment',
                'description': f'样式组{i+1}使用{alignment_type}对齐',
                'regions': [region['id'] for region in group],
                'priority': 'high' if len(group) > 2 else 'medium'
            }
            strategies.append(strategy)

        return strategies

    def _analyze_group_alignment(self, group: List[Dict]) -> str:
        """分析组内的主要对齐方式"""
        if len(group) < 2:
            return 'center'
        
        # 分析左边界对齐
        left_positions = [region['left'] for region in group]
        left_variance = self._calculate_variance(left_positions)
        
        # 分析右边界对齐
        right_positions = [region['right'] for region in group]
        right_variance = self._calculate_variance(right_positions)
        
        # 分析中心对齐
        center_positions = [region['center'][0] for region in group]
        center_variance = self._calculate_variance(center_positions)
        
        # 选择方差最小的对齐方式
        min_variance = min(left_variance, right_variance, center_variance)
        
        if min_variance == left_variance and left_variance < 10:
            return 'left'
        elif min_variance == right_variance and right_variance < 10:
            return 'right'
        elif min_variance == center_variance and center_variance < 10:
            return 'center'
        else:
            return 'distributed'

    def _calculate_variance(self, values: List[int]) -> float:
        """计算数值列表的方差"""
        if not values:
            return 0
        
        mean = sum(values) / len(values)
        variance = sum((x - mean) ** 2 for x in values) / len(values)
        return variance

    def _create_simple_layout_result(self, regions: List[TextRegion]) -> LayoutResult:
        """创建简单布局结果"""
        regions_data = self._convert_regions_to_dict(regions)

        return LayoutResult(
            layout_mode='single_text' if len(regions) <= 1 else 'simple',
            regions=regions_data,
            horizontal_alignment={'type': 'single', 'left_groups': [], 'center_groups': [], 'right_groups': [], 'distribution_groups': []},
            vertical_distribution={'type': 'single', 'rows': 1, 'columns': 1},
            alignment_strategies=[{
                'type': 'center_alignment',
                'description': '使用居中对齐',
                'regions': 'all',
                'priority': 'default'
            }]
        )

    def _print_layout_summary(self, result: LayoutResult):
        """打印布局分析摘要"""
        print(f"布局结果: {result.layout_mode} ({len(result.regions)}个区域)")
        print(f"  水平对齐: {result.horizontal_alignment['type']}")
        print(f"  垂直分布: {result.vertical_distribution['type']} "
              f"({result.vertical_distribution['rows']}行×{result.vertical_distribution['columns']}列)")

        # 打印分组信息
        h_align = result.horizontal_alignment
        group_info = []

        if h_align.get('left_groups'):
            group_info.append(f"left_group组{len(h_align['left_groups'])}个")
        if h_align.get('center_groups'):
            group_info.append(f"center_group组{len(h_align['center_groups'])}个")
        if h_align.get('right_groups'):
            group_info.append(f"right_group组{len(h_align['right_groups'])}个")
        if h_align.get('distribution_groups'):
            group_info.append(f"分布组{len(h_align['distribution_groups'])}个")

        if group_info:
            print(f"  分组: {', '.join(group_info)}")

        # 打印详细分组（仅分布组）
        for i, group in enumerate(h_align.get('distribution_groups', [])):
            texts = [region['text'] for region in group]
            print(f"    分布组{i+1}: {texts}")

    def get_alignment_for_region(self, region_bbox: Tuple[int, int, int, int], layout_result: LayoutResult) -> str:
        """
        为特定区域获取对齐方式

        Args:
            region_bbox: 区域边界框 (x, y, w, h)
            layout_result: 布局分析结果

        Returns:
            str: 对齐方式 ('left', 'center', 'right')
        """
        x, y, w, _ = region_bbox
        center_x = x + w // 2

        try:
            h_align = layout_result.horizontal_alignment

            # 检查左对齐组
            for group in h_align.get('left_groups', []):
                for region in group:
                    if (abs(region.get('left', 0) - x) <= self.alignment_threshold and
                        abs(region.get('top', 0) - y) <= self.alignment_threshold):
                        return 'left'

            # 检查右对齐组
            for group in h_align.get('right_groups', []):
                for region in group:
                    if (abs(region.get('right', 0) - (x + w)) <= self.alignment_threshold and
                        abs(region.get('top', 0) - y) <= self.alignment_threshold):
                        return 'right'

            # 检查居中对齐组
            for group in h_align.get('center_groups', []):
                for region in group:
                    region_center_x = region.get('center', [0, 0])[0]
                    if abs(region_center_x - center_x) <= self.alignment_threshold:
                        return 'center'

            # 检查分布组
            for group in h_align.get('distribution_groups', []):
                for region in group:
                    if (abs(region.get('left', 0) - x) <= self.alignment_threshold and
                        abs(region.get('top', 0) - y) <= self.alignment_threshold):
                        # 对于分布组，根据在组内的相对位置确定对齐方式
                        return self._determine_alignment_in_distribution_group(region, group)

            # 默认居中对齐
            return 'center'

        except Exception as e:
            print(f"获取区域对齐方式失败: {e}")
            return 'center'
    
    def _determine_alignment_in_distribution_group(self, target_region: Dict, group: List[Dict]) -> str:
        """在分布组内确定区域的对齐方式"""
        if len(group) < 2:
            return 'center'
        
        # 按X坐标排序
        sorted_group = sorted(group, key=lambda r: r['center'][0])
        
        # 找到目标区域在组内的位置
        target_center_x = target_region['center'][0]
        
        # 如果是最左边的区域，使用左对齐
        if target_center_x == sorted_group[0]['center'][0]:
            return 'left'
        # 如果是最右边的区域，使用右对齐
        elif target_center_x == sorted_group[-1]['center'][0]:
            return 'right'
        # 中间的区域使用居中对齐
        else:
            return 'center'







    def _generate_debug_outputs(self, chinese_regions: List[TextRegion], layout_result: LayoutResult):
        """生成重构后的调试图像 - 只保留样式分组，并为每种对齐方式和分组生成单独图片"""
        try:
            debug_dir = self.config_manager.config.layout_debug_dir
            os.makedirs(debug_dir, exist_ok=True)

            base_image = self._get_base_image(chinese_regions)

            # 1. 保留样式分组图片
            self._save_style_grouping_debug(base_image, layout_result, debug_dir, chinese_regions)
            
            # 2. 为每种对齐方式生成单独图片
            self._save_individual_alignment_debug(base_image, layout_result, debug_dir, chinese_regions)
            
            # 3. 为每个分组生成单独图片，说明分组原因
            self._save_individual_group_debug(base_image, layout_result, debug_dir, chinese_regions)
            
            # 生成综合布局数据
            self._save_layout_data_json(layout_result, debug_dir)

            print(f"📊 布局调试文件已保存到: {debug_dir}")
            print("   - style_grouping.png (样式分组总览)")
            print("   - alignment_*.png (各对齐方式)")
            print("   - group_*.png (各分组详情)")

        except Exception as e:
            print(f"❌ 生成布局调试文件失败: {e}")

    def _save_individual_alignment_debug(self, base_image: np.ndarray, layout_result: LayoutResult, debug_dir: str, chinese_regions: List[TextRegion]):
        """为每种对齐方式生成单独的调试图片"""
        try:
            h_align = layout_result.horizontal_alignment
            region_map = {region.id: region for region in chinese_regions}
            
            # 定义对齐类型配置
            alignment_configs = [
                ('left_groups', 'left_aligned', '左对齐', 'left'),
                ('center_groups', 'center_aligned', '居中对齐', 'center'),
                ('right_groups', 'right_aligned', '右对齐', 'right'),
                ('distribution_groups', 'distribution', '分布对齐', 'distribution')
            ]
            
            for group_key, color_key, label, align_type in alignment_configs:
                groups = h_align.get(group_key, [])
                if not groups:
                    continue
                
                # 为每种对齐方式创建单独图片
                pil_image = Image.fromarray(cv2.cvtColor(base_image, cv2.COLOR_BGR2RGB))
                overlay = Image.new('RGBA', pil_image.size, (0, 0, 0, 0))
                draw = ImageDraw.Draw(overlay)
                
                font_large = self._load_chinese_font(32)
                font_medium = self._load_chinese_font(24)
                font_small = self._load_chinese_font(18)
                
                # 使用深色文字
                text_color = (40, 40, 40, 255)  # 深灰色
                highlight_color = (*self.DEBUG_COLORS[color_key], 200)
                
                # 绘制标题
                title = f"{label} ({len(groups)}组)"
                draw.text((30, 30), title, fill=text_color, font=font_large)
                
                # 绘制说明
                desc = f"显示所有{label}的文本区域，用透明{label}颜色标记"
                draw.text((30, 80), desc, fill=text_color, font=font_medium)
                
                # 绘制所有该类型的对齐组
                for group_idx, group in enumerate(groups):
                    if not group:
                        continue
                    
                    # 绘制分组区域
                    for region_dict in group:
                        self._draw_enhanced_region_box(draw, region_dict, region_map, highlight_color, 
                                                     show_content=True, font=font_small)
                    
                    # 绘制对齐线
                    self._draw_enhanced_alignment_line(draw, group, align_type, highlight_color)
                    
                    # 标记组号
                    if group:
                        first_region = group[0]
                        group_label = f"{label}组{group_idx + 1}"
                        draw.text((first_region['left'] - 10, first_region['top'] - 30), 
                                group_label, fill=text_color, font=font_medium)
                
                # 合并图层并保存
                final_image = Image.alpha_composite(pil_image.convert('RGBA'), overlay)
                final_image = final_image.convert('RGB')
                cv2_image = cv2.cvtColor(np.array(final_image), cv2.COLOR_RGB2BGR)
                
                filename = f"alignment_{align_type}.png"
                cv2.imwrite(os.path.join(debug_dir, filename), cv2_image)
                print(f"✅ {label}图片已保存: {filename}")

        except Exception as e:
            print(f"❌ 保存对齐方式图片失败: {e}")

    def _save_individual_group_debug(self, base_image: np.ndarray, layout_result: LayoutResult, debug_dir: str, chinese_regions: List[TextRegion]):
        """为每个分组生成单独的调试图片，说明分组原因"""
        try:
            region_map = {region.id: region for region in chinese_regions}
            
            # 获取样式分组（使用distribution_groups作为样式分组结果）
            style_groups = layout_result.horizontal_alignment.get('distribution_groups', [])
            
            # 样式分组颜色
            style_colors = [
                'style_group_1', 'style_group_2', 'style_group_3', 'style_group_4',
                'style_group_5', 'style_group_6', 'style_group_7', 'style_group_8'
            ]
            
            for group_idx, group in enumerate(style_groups):
                if not group:
                    continue
                
                # 为每个分组创建单独图片
                pil_image = Image.fromarray(cv2.cvtColor(base_image, cv2.COLOR_BGR2RGB))
                overlay = Image.new('RGBA', pil_image.size, (0, 0, 0, 0))
                draw = ImageDraw.Draw(overlay)
                
                font_large = self._load_chinese_font(32)
                font_medium = self._load_chinese_font(24)
                font_small = self._load_chinese_font(18)
                
                # 使用深色文字
                text_color = (40, 40, 40, 255)  # 深灰色
                color_key = style_colors[group_idx % len(style_colors)]
                highlight_color = (*self.DEBUG_COLORS[color_key], 200)
                
                # 收集分组信息
                group_texts = []
                unified_styles = {
                    'fonts': [],
                    'colors': [],
                    'heights': []
                }
                
                # 分析分组原因
                for region_dict in group:
                    group_texts.append(region_dict['text'])
                    
                    # 收集样式信息
                    text_region = region_map.get(region_dict['id'])
                    if text_region and text_region.style_info:
                        style_info = text_region.style_info
                        unified_styles['fonts'].append(style_info.get('matched_font', '未知'))
                        unified_styles['colors'].append(style_info.get('color', (0, 0, 0)))
                        unified_styles['heights'].append(style_info.get('precise_height', 0))
                
                # 绘制标题
                title = f"样式分组 {group_idx + 1} ({len(group)}个区域)"
                draw.text((30, 30), title, fill=text_color, font=font_large)
                
                # 分析并显示分组原因
                reasons = []
                if unified_styles['fonts']:
                    most_common_font = max(set(unified_styles['fonts']), key=unified_styles['fonts'].count)
                    font_ratio = unified_styles['fonts'].count(most_common_font) / len(unified_styles['fonts'])
                    if font_ratio >= 0.6:
                        reasons.append(f"字体相似: {most_common_font}")
                
                if unified_styles['colors']:
                    # 简化颜色相似性检查
                    color_groups = {}
                    for color in unified_styles['colors']:
                        color_key = f"{color[0]//30}_{color[1]//30}_{color[2]//30}"  # 颜色分组
                        color_groups[color_key] = color_groups.get(color_key, 0) + 1
                    
                    if len(color_groups) <= 2:  # 颜色种类较少
                        reasons.append("颜色相似")
                
                if unified_styles['heights']:
                    height_variance = self._calculate_variance(unified_styles['heights'])
                    if height_variance < 10:  # 高度差异小
                        avg_height = sum(unified_styles['heights']) / len(unified_styles['heights'])
                        reasons.append(f"高度相似: {avg_height:.0f}px")
                
                # 位置关系分析
                if len(group) >= 2:
                    # 检查是否在同一行
                    y_positions = [region['top'] for region in group]
                    if max(y_positions) - min(y_positions) < 20:
                        reasons.append("同行排列")
                    
                    # 检查是否垂直对齐
                    x_positions = [region['left'] for region in group]
                    if max(x_positions) - min(x_positions) < 20:
                        reasons.append("垂直对齐")
                
                if not reasons:
                    reasons.append("语义相关")
                
                # 显示分组原因
                reason_text = f"分组原因: {' + '.join(reasons)}"
                draw.text((30, 80), reason_text, fill=text_color, font=font_medium)
                
                # 显示分组内容
                content_text = f"包含文本: {' | '.join(group_texts)}"
                draw.text((30, 120), content_text, fill=text_color, font=font_medium)
                
                # 绘制分组区域
                for region_dict in group:
                    self._draw_enhanced_region_box(draw, region_dict, region_map, highlight_color, 
                                                 show_content=True, font=font_small)
                
                # 绘制连接线显示分组关系
                self._draw_style_connections(draw, group, highlight_color)
                
                # 合并图层并保存
                final_image = Image.alpha_composite(pil_image.convert('RGBA'), overlay)
                final_image = final_image.convert('RGB')
                cv2_image = cv2.cvtColor(np.array(final_image), cv2.COLOR_RGB2BGR)
                
                filename = f"group_{group_idx + 1}.png"
                cv2.imwrite(os.path.join(debug_dir, filename), cv2_image)
                print(f"✅ 分组{group_idx + 1}图片已保存: {filename}")

        except Exception as e:
            print(f"❌ 保存分组图片失败: {e}")



    def _save_style_grouping_debug(self, base_image: np.ndarray, layout_result: LayoutResult, debug_dir: str, chinese_regions: List[TextRegion]):
        """保存样式分组图 - 重点显示样式相似性分组"""
        try:
            pil_image = Image.fromarray(cv2.cvtColor(base_image, cv2.COLOR_BGR2RGB))
            overlay = Image.new('RGBA', pil_image.size, (0, 0, 0, 0))
            draw = ImageDraw.Draw(overlay)
            
            font_large = self._load_chinese_font(28)
            font_medium = self._load_chinese_font(20)
            font_small = self._load_chinese_font(16)

            # 绘制标题
            draw.text((20, 20), "样式分组分析", fill=(*self.DEBUG_COLORS['text_primary'], 255), font=font_large)
            
            # 构建区域映射
            region_map = {region.id: region for region in chinese_regions}
            
            # 获取样式分组（使用distribution_groups作为样式分组结果）
            style_groups = layout_result.horizontal_alignment.get('distribution_groups', [])
            
            # 样式分组颜色
            style_colors = [
                'style_group_1', 'style_group_2', 'style_group_3', 'style_group_4',
                'style_group_5', 'style_group_6', 'style_group_7', 'style_group_8'
            ]
            
            group_info = []
            
            for group_idx, group in enumerate(style_groups):
                if not group:
                    continue
                    
                color_key = style_colors[group_idx % len(style_colors)]
                color = (*self.DEBUG_COLORS[color_key], 200)
                
                # 收集分组信息
                group_texts = []
                unified_styles = {
                    'fonts': [],
                    'colors': [],
                    'heights': []
                }
                
                # 绘制分组区域
                for region_dict in group:
                    # 绘制区域框
                    self._draw_enhanced_region_box(draw, region_dict, region_map, color, 
                                                 show_content=True, font=font_small)
                    group_texts.append(region_dict['text'])
                    
                    # 收集样式信息
                    text_region = region_map.get(region_dict['id'])
                    if text_region and text_region.style_info:
                        style_info = text_region.style_info
                        unified_styles['fonts'].append(style_info.get('matched_font', '未知'))
                        unified_styles['colors'].append(style_info.get('color', (0, 0, 0)))
                        unified_styles['heights'].append(style_info.get('precise_height', 0))
                
                # 绘制分组连接
                if len(group) > 1:
                    self._draw_style_connections(draw, group, color)
                
                # 记录分组信息
                group_info.append({
                    'index': group_idx + 1,
                    'texts': group_texts,
                    'unified_styles': unified_styles,
                    'color': color,
                    'count': len(group)
                })
            
            # 绘制样式分组图例
            self._draw_style_grouping_legend(draw, group_info, font_medium, font_small)
            
            # 合并图层并保存
            final_image = Image.alpha_composite(pil_image.convert('RGBA'), overlay)
            final_image = final_image.convert('RGB')
            cv2_image = cv2.cvtColor(np.array(final_image), cv2.COLOR_RGB2BGR)
            cv2.imwrite(os.path.join(debug_dir, "style_grouping.png"), cv2_image)
            
            print("✅ 样式分组图已保存")

        except Exception as e:
            print(f"❌ 保存样式分组图失败: {e}")



    def _draw_enhanced_region_box(self, draw: ImageDraw.Draw, region: Dict, region_map: Dict, color: tuple, show_content: bool = True, font=None):
        """绘制增强的区域框"""
        # 绘制区域边框
        draw.rectangle([region['left'], region['top'], region['right'], region['bottom']],
                      outline=color, width=2)
        
        # 绘制半透明填充
        if len(color) == 4:  # RGBA
            fill_color = (*color[:3], 30)
            draw.rectangle([region['left'], region['top'], region['right'], region['bottom']],
                          fill=fill_color)
        
        if show_content and font:
            # 绘制文本内容
            text = region['text']
            if len(text) > 10:
                text = text[:8] + "..."
            
            # 计算文本位置
            try:
                bbox = draw.textbbox((0, 0), text, font=font)
                text_width = bbox[2] - bbox[0]
                text_height = bbox[3] - bbox[1]
            except:
                text_width, text_height = len(text) * 8, 16
            
            # 文本背景
            text_x = region['left'] + 2
            text_y = region['top'] - text_height - 2 if region['top'] > text_height + 2 else region['bottom'] + 2
            
            draw.rectangle([text_x, text_y, text_x + text_width + 4, text_y + text_height + 2],
                          fill=(*self.DEBUG_COLORS['background'], 220))
            draw.text((text_x + 2, text_y + 1), text, fill=(*self.DEBUG_COLORS['text_primary'], 255), font=font)

    def _draw_enhanced_alignment_line(self, draw: ImageDraw.Draw, group: List[Dict], align_type: str, color: tuple):
        """绘制增强的对齐线"""
        if not group:
            return
            
        line_color = (*self.DEBUG_COLORS['alignment_line'], 200)
        
        if align_type == 'left':
            x = min(region['left'] for region in group)
            y_start = min(region['top'] for region in group) - 20
            y_end = max(region['bottom'] for region in group) + 20
            # 绘制粗对齐线
            draw.line([(x, y_start), (x, y_end)], fill=line_color, width=3)
            # 绘制箭头标记
            draw.polygon([(x-5, y_start), (x+5, y_start), (x, y_start-10)], fill=line_color)
            
        elif align_type == 'right':
            x = max(region['right'] for region in group)
            y_start = min(region['top'] for region in group) - 20
            y_end = max(region['bottom'] for region in group) + 20
            draw.line([(x, y_start), (x, y_end)], fill=line_color, width=3)
            draw.polygon([(x-5, y_start), (x+5, y_start), (x, y_start-10)], fill=line_color)
            
        elif align_type == 'center':
            x = sum(region['center'][0] for region in group) / len(group)
            y_start = min(region['top'] for region in group) - 20
            y_end = max(region['bottom'] for region in group) + 20
            draw.line([(x, y_start), (x, y_end)], fill=line_color, width=3)
            draw.polygon([(x-5, y_start), (x+5, y_start), (x, y_start-10)], fill=line_color)
            
        elif align_type == 'distribution' and len(group) >= 2:
            y = sum(region['center'][1] for region in group) / len(group)
            x_start = min(region['left'] for region in group) - 20
            x_end = max(region['right'] for region in group) + 20
            draw.line([(x_start, y), (x_end, y)], fill=line_color, width=3)
            # 绘制分布标记
            for region in group:
                center_x = region['center'][0]
                draw.ellipse([center_x-4, y-4, center_x+4, y+4], fill=line_color)

    def _draw_style_connections(self, draw: ImageDraw.Draw, group: List[Dict], color: tuple):
        """绘制样式分组的连接线"""
        if len(group) < 2:
            return
        
        # 计算分组重心
        center_x = sum(region['center'][0] for region in group) / len(group)
        center_y = sum(region['center'][1] for region in group) / len(group)
        
        connection_color = (*self.DEBUG_COLORS['connection_line'], 150)
        
        # 从重心向每个区域绘制连接线
        for region in group:
            region_center = region['center']
            draw.line([region_center, (center_x, center_y)], fill=connection_color, width=2)
        
        # 绘制重心点
        draw.ellipse([center_x-8, center_y-8, center_x+8, center_y+8], 
                    fill=color, outline=(*self.DEBUG_COLORS['text_primary'], 200), width=2)

    def _draw_alignment_legend(self, draw: ImageDraw.Draw, group_stats: List[Dict], ungrouped_count: int, font_medium, font_small):
        """绘制对齐分析图例"""
        legend_x = 20
        legend_y = 80
        
        # 图例标题
        draw.text((legend_x, legend_y), "对齐分组统计:", fill=(*self.DEBUG_COLORS['text_primary'], 255), font=font_medium)
        legend_y += 35
        
        # 分组统计
        for group in group_stats:
            # 颜色块
            draw.rectangle([legend_x, legend_y, legend_x + 20, legend_y + 15], fill=group['color'])
            
            # 文本信息
            text = f"{group['type']} {group['index']}: {group['count']}个区域"
            draw.text((legend_x + 25, legend_y), text, fill=(*self.DEBUG_COLORS['text_primary'], 255), font=font_small)
            legend_y += 25
        
        # 未分组统计
        if ungrouped_count > 0:
            ungrouped_color = (*self.DEBUG_COLORS['ungrouped'], 200)
            draw.rectangle([legend_x, legend_y, legend_x + 20, legend_y + 15], fill=ungrouped_color)
            draw.text((legend_x + 25, legend_y), f"未分组: {ungrouped_count}个区域", 
                     fill=(*self.DEBUG_COLORS['text_primary'], 255), font=font_small)

    def _draw_style_grouping_legend(self, draw: ImageDraw.Draw, group_info: List[Dict], font_medium, font_small):
        """绘制样式分组图例"""
        legend_x = 20
        legend_y = 80
        
        # 图例标题
        draw.text((legend_x, legend_y), "样式分组详情:", fill=(*self.DEBUG_COLORS['text_primary'], 255), font=font_medium)
        legend_y += 35
        
        for group in group_info:
            # 分组标题
            draw.rectangle([legend_x, legend_y, legend_x + 20, legend_y + 15], fill=group['color'])
            group_title = f"样式组 {group['index']} ({group['count']}个)"
            draw.text((legend_x + 25, legend_y), group_title, fill=(*self.DEBUG_COLORS['text_primary'], 255), font=font_small)
            legend_y += 25
            
            # 样式信息
            styles = group['unified_styles']
            if styles['fonts']:
                font_name = styles['fonts'][0] if styles['fonts'] else "未知"
                draw.text((legend_x + 10, legend_y), f"字体: {font_name}", 
                         fill=(*self.DEBUG_COLORS['text_secondary'], 255), font=font_small)
                legend_y += 20
            
            if styles['heights']:
                height = styles['heights'][0] if styles['heights'] else 0
                draw.text((legend_x + 10, legend_y), f"高度: {height}px", 
                         fill=(*self.DEBUG_COLORS['text_secondary'], 255), font=font_small)
                legend_y += 20
            
            # 文本列表（最多显示3个）
            texts_to_show = group['texts'][:3]
            for text in texts_to_show:
                display_text = text if len(text) <= 15 else text[:12] + "..."
                draw.text((legend_x + 10, legend_y), f"• {display_text}", 
                         fill=(*self.DEBUG_COLORS['text_secondary'], 255), font=font_small)
                legend_y += 18
            
            if len(group['texts']) > 3:
                draw.text((legend_x + 10, legend_y), f"... 等{len(group['texts'])}个", 
                         fill=(*self.DEBUG_COLORS['text_secondary'], 255), font=font_small)
                legend_y += 18
            
            legend_y += 10  # 分组间距

    def _draw_layout_grid(self, draw: ImageDraw.Draw, v_dist: Dict, image_size: Tuple[int, int]):
        """绘制布局网格"""
        width, height = image_size
        grid_color = (*self.DEBUG_COLORS['grid_line'], 100)
        
        # 绘制行网格
        for row_positions in v_dist.get('row_groups', []):
            if row_positions:
                y = sum(row_positions) / len(row_positions)
                # 虚线效果
                for x in range(0, width, 15):
                    draw.line([(x, y), (x + 8, y)], fill=grid_color, width=1)
        
        # 绘制列网格
        for col_positions in v_dist.get('column_groups', []):
            if col_positions:
                x = sum(col_positions) / len(col_positions)
                # 虚线效果
                for y in range(0, height, 15):
                    draw.line([(x, y), (x, y + 8)], fill=grid_color, width=1)

    def _draw_enhanced_grid_system(self, draw: ImageDraw.Draw, v_dist: Dict, image_size: Tuple[int, int]):
        """绘制增强的网格系统"""
        width, height = image_size
        
        # 主网格线
        main_grid_color = (*self.DEBUG_COLORS['grid_line'], 150)
        # 次网格线
        sub_grid_color = (*self.DEBUG_COLORS['grid_line'], 80)
        
        # 绘制主要行网格
        for i, row_positions in enumerate(v_dist.get('row_groups', [])):
            if row_positions:
                y = sum(row_positions) / len(row_positions)
                draw.line([(0, y), (width, y)], fill=main_grid_color, width=2)
                # 添加行标签
                draw.text((5, y - 15), f"行{i+1}", fill=(*self.DEBUG_COLORS['text_secondary'], 200), 
                         font=self._load_chinese_font(14))
        
        # 绘制主要列网格
        for i, col_positions in enumerate(v_dist.get('column_groups', [])):
            if col_positions:
                x = sum(col_positions) / len(col_positions)
                draw.line([(x, 0), (x, height)], fill=main_grid_color, width=2)
                # 添加列标签
                draw.text((x + 5, 5), f"列{i+1}", fill=(*self.DEBUG_COLORS['text_secondary'], 200), 
                         font=self._load_chinese_font(14))

    def _draw_layout_statistics(self, draw: ImageDraw.Draw, layout_result: LayoutResult, font_medium, image_size: Tuple[int, int]):
        """绘制布局统计信息"""
        width, height = image_size
        stats_x = width - 300
        stats_y = 20
        
        # 背景框
        draw.rectangle([stats_x - 10, stats_y - 10, width - 10, stats_y + 120], 
                      fill=(*self.DEBUG_COLORS['background'], 200))
        
        # 统计信息
        stats = [
            f"布局模式: {layout_result.layout_mode}",
            f"总区域数: {len(layout_result.regions)}",
            f"网格: {layout_result.vertical_distribution.get('rows', 0)}行×{layout_result.vertical_distribution.get('columns', 0)}列",
            f"主要对齐: {layout_result.horizontal_alignment.get('type', '未知')}"
        ]
        
        for i, stat in enumerate(stats):
            draw.text((stats_x, stats_y + i * 25), stat, 
                     fill=(*self.DEBUG_COLORS['text_primary'], 255), font=font_medium)

    def _draw_spatial_statistics(self, draw: ImageDraw.Draw, v_dist: Dict, font_medium, font_small, image_size: Tuple[int, int]):
        """绘制空间分布统计"""
        width, height = image_size
        stats_x = width - 250
        stats_y = 20
        
        # 背景框
        draw.rectangle([stats_x - 10, stats_y - 10, width - 10, stats_y + 150], 
                      fill=(*self.DEBUG_COLORS['background'], 220))
        
        # 标题
        draw.text((stats_x, stats_y), "空间分布统计", fill=(*self.DEBUG_COLORS['text_primary'], 255), font=font_medium)
        stats_y += 30
        
        # 统计信息
        stats = [
            f"分布类型: {v_dist.get('type', '未知')}",
            f"网格规模: {v_dist.get('rows', 0)}×{v_dist.get('columns', 0)}",
            f"行组数: {len(v_dist.get('row_groups', []))}",
            f"列组数: {len(v_dist.get('column_groups', []))}",
        ]
        
        for stat in stats:
            draw.text((stats_x, stats_y), stat, fill=(*self.DEBUG_COLORS['text_primary'], 255), font=font_small)
            stats_y += 20

    def _get_base_image(self, regions: List[TextRegion]) -> np.ndarray:
        """获取基础图像"""
        # 尝试加载原始图像
        try:
            image_path = "example.jpg"
            if os.path.exists(image_path):
                return cv2.imread(image_path)
        except:
            pass

        # 创建基础图像
        if not regions:
            return np.ones((600, 800, 3), dtype=np.uint8) * 255

        # 计算边界并创建图像
        all_coords = []
        for region in regions:
            x, y, w, h = region.bbox
            all_coords.extend([(x, y), (x + w, y + h)])

        min_x = min(coord[0] for coord in all_coords)
        max_x = max(coord[0] for coord in all_coords)
        min_y = min(coord[1] for coord in all_coords)
        max_y = max(coord[1] for coord in all_coords)

        margin = 50
        width = max_x - min_x + 2 * margin
        height = max_y - min_y + 2 * margin

        return np.ones((height, width, 3), dtype=np.uint8) * 255

    def _load_chinese_font(self, size=20):
        """加载中文字体"""
        try:
            return ImageFont.truetype("/System/Library/Fonts/PingFang.ttc", size)
        except:
            try:
                font_path = os.path.join(self.config_manager.get_fonts_dir(), "思源黑体", "SourceHanSans-VF.otf")
                return ImageFont.truetype(font_path, size)
            except:
                return ImageFont.load_default()
    
    def _save_layout_data_json(self, layout_result: LayoutResult, debug_dir: str):
        """保存布局数据为JSON格式"""
        layout_data = {
            "metadata": {
                "timestamp": self._get_timestamp(),
                "total_regions": len(layout_result.regions),
                "layout_mode": layout_result.layout_mode,
                "alignment_threshold": self.alignment_threshold,
                "proximity_threshold": self.proximity_threshold
            },
            "horizontal_alignment": {
                "type": layout_result.horizontal_alignment.get('type', 'unknown'),
                **{f"{group_type}_groups": [
                    [self._serialize_region(region) for region in group]
                    for group in layout_result.horizontal_alignment.get(f'{group_type}_groups', [])
                ] for group_type in ['left', 'center', 'right', 'distribution']}
            },
            "vertical_distribution": {
                "type": layout_result.vertical_distribution.get('type', 'unknown'),
                "rows": layout_result.vertical_distribution.get('rows', 0),
                "columns": layout_result.vertical_distribution.get('columns', 0),
                "row_groups": layout_result.vertical_distribution.get('row_groups', []),
                "column_groups": layout_result.vertical_distribution.get('column_groups', [])
            },
            "alignment_strategies": [
                {
                    "type": strategy.get('type', ''),
                    "description": strategy.get('description', ''),
                    "regions": strategy.get('regions', ''),
                    "priority": strategy.get('priority', '')
                }
                for strategy in layout_result.alignment_strategies
            ],
            "regions_details": [self._serialize_region(region) for region in layout_result.regions]
        }

        # 保存JSON文件
        output_path = os.path.join(debug_dir, "layout_data.json")
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(layout_data, f, ensure_ascii=False, indent=2)

    def _serialize_region(self, region: Dict) -> Dict:
        """序列化区域数据"""
        return {
            "id": region.get('id', 0),
            "text": region.get('text', ''),
            "bbox": region.get('bbox', [0, 0, 0, 0]),
            "left": region.get('left', 0),
            "right": region.get('right', 0),
            "top": region.get('top', 0),
            "bottom": region.get('bottom', 0),
            "center": region.get('center', [0, 0])
        }

    def _get_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    def _color_distance(self, color1: Tuple[int, int, int], color2: Tuple[int, int, int]) -> float:
        """计算两个颜色的欧几里得距离"""
        return ((color1[0] - color2[0])**2 + (color1[1] - color2[1])**2 + (color1[2] - color2[2])**2)**0.5
    
    def _choose_unified_color(self, colors: List[Tuple[int, int, int]]) -> Tuple[int, int, int]:
        """智能选择统一颜色：基于频次+相似度聚类"""
        if not colors:
            return (0, 0, 0)
        
        # 颜色聚类：将相似颜色归为一类（RGB差值<25为相似）
        color_groups = []
        similarity_threshold = 25
        
        for color in colors:
            found_group = False
            for group in color_groups:
                # 检查是否与现有组相似
                representative = group[0]
                if self._color_distance(color, representative) <= similarity_threshold:
                    group.append(color)
                    found_group = True
                    break
            
            if not found_group:
                color_groups.append([color])
        
        # 选择最大组的代表色（组内平均色）
        largest_group = max(color_groups, key=len)
        
        print(f"    颜色分析: 发现{len(color_groups)}个颜色组，最大组有{len(largest_group)}个颜色")
        
        # 计算组内平均颜色作为代表色
        avg_r = sum(c[0] for c in largest_group) // len(largest_group)
        avg_g = sum(c[1] for c in largest_group) // len(largest_group)
        avg_b = sum(c[2] for c in largest_group) // len(largest_group)
        
        unified_color = (avg_r, avg_g, avg_b)
        print(f"    → 选择统一颜色: {unified_color} (基于{len(largest_group)}个相似颜色)")
        return unified_color
    
    def _choose_unified_font(self, fonts: List[str], text_regions: List) -> str:
        """智能选择统一字体：基于频次+置信度"""
        if not fonts:
            return "思源黑体"
        
        # 收集字体信息：频次 + 置信度
        font_data = {}
        for i, font in enumerate(fonts):
            if font and i < len(text_regions):
                text_region = text_regions[i]
                confidence = 0.5  # 默认置信度
                
                # 从style_info获取置信度
                if text_region.style_info and 'font_confidence' in text_region.style_info:
                    confidence = text_region.style_info['font_confidence']
                
                if font not in font_data:
                    font_data[font] = {'count': 0, 'total_confidence': 0.0}
                
                font_data[font]['count'] += 1
                font_data[font]['total_confidence'] += confidence
        
        if not font_data:
            return "思源黑体"
        
        # 计算加权得分：频次 × 平均置信度
        font_scores = {}
        for font, data in font_data.items():
            avg_confidence = data['total_confidence'] / data['count']
            score = data['count'] * avg_confidence
            font_scores[font] = score
            print(f"    字体分析: {font} - 出现{data['count']}次, 平均置信度{avg_confidence:.3f}, 得分{score:.3f}")
        
        # 选择得分最高的字体
        best_font = max(font_scores.items(), key=lambda x: x[1])[0]
        print(f"    → 选择统一字体: {best_font}")
        return best_font
    
    def _simplify_font_name(self, font_name: str) -> str:
        """简化字体名称"""
        if not font_name:
            return ""
        
        # 去除常见后缀
        suffixes = ['Bold', 'Black', 'Regular', 'Medium', 'Light', 'SC', 'TC', 'CN']
        simple_name = font_name
        for suffix in suffixes:
            simple_name = simple_name.replace(suffix, '').strip()
        
        return simple_name
    

