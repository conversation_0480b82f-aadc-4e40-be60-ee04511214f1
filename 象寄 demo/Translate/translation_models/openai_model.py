"""
OpenAI翻译模型 - 通过OpenRouter API调用
"""
import requests
import json
import time
from typing import Dict, Any, List
from .base import BaseTranslationModel, LLMTranslationResult


class OpenAITranslationModel(BaseTranslationModel):
    """OpenAI翻译模型 - 使用OpenRouter API"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化OpenAI翻译模型
        
        Args:
            config: 模型配置，包含api_key等
        """
        super().__init__(config)
        
        self.api_key = config.get('api_key', '')
        self.base_url = config.get('base_url', 'https://openrouter.ai/api/v1')
        self.model = config.get('model', 'openai/gpt-4o-mini')
        self.temperature = config.get('temperature', 0.1)
        self.max_tokens = config.get('max_tokens', 1000)
        
        if not self.api_key:
            raise ValueError("OpenAI API密钥未配置")
    
    def translate(self, text: str, source_lang: str = "zh", target_lang: str = "ja") -> LLMTranslationResult:
        """
        翻译单个文本
        
        Args:
            text: 要翻译的文本
            source_lang: 源语言代码
            target_lang: 目标语言代码
            
        Returns:
            LLMTranslationResult: 翻译结果
        """
        if not text.strip():
            return LLMTranslationResult(
                original_text=text,
                translated_text=text,
                success=True,
                confidence=1.0
            )
        
        # 构建翻译提示词
        prompt = self._build_translation_prompt(text, source_lang, target_lang)
        
        # 调试信息：输出发送的内容
        print(f"\n=== 发送给大模型的内容 ===")
        print(prompt)
        print(f"=== 发送内容结束 ===\n")
        
        # 重试机制
        for attempt in range(self.max_retries):
            try:
                response = self._make_api_request(prompt)
                
                # 调试信息：输出返回的内容
                print(f"\n=== 大模型返回的内容 ===")
                print(f"完整响应: {response}")
                print(f"=== 返回内容结束 ===\n")
                
                if response.get('error'):
                    error_msg = response['error'].get('message', '未知错误')
                    print(f"API错误 (尝试 {attempt + 1}): {error_msg}")
                    if attempt == self.max_retries - 1:
                        return LLMTranslationResult(
                            original_text=text,
                            translated_text=text,
                            success=False,
                            error=f"API错误: {error_msg}",
                            confidence=0.0
                        )
                    time.sleep(2 ** attempt)  # 指数退避
                    continue
                
                # 提取翻译结果
                translated_text = self._extract_translation(response)
                
                return LLMTranslationResult(
                    original_text=text,
                    translated_text=translated_text,
                    success=True,
                    confidence=0.9  # OpenAI模型一般质量较高
                )
                
            except Exception as e:
                print(f"翻译请求失败 (尝试 {attempt + 1}): {str(e)}")
                if attempt == self.max_retries - 1:
                    return LLMTranslationResult(
                        original_text=text,
                        translated_text=text,
                        success=False,
                        error=str(e),
                        confidence=0.0
                    )
                time.sleep(2 ** attempt)
        
        # 所有重试都失败
        return LLMTranslationResult(
            original_text=text,
            translated_text=text,
            success=False,
            error="所有重试都失败",
            confidence=0.0
        )
    
    def batch_translate(self, texts: List[str], source_lang: str = "zh", target_lang: str = "ja") -> List[LLMTranslationResult]:
        """
        批量翻译文本 - 一次API请求处理所有文本
        
        Args:
            texts: 要翻译的文本列表
            source_lang: 源语言代码
            target_lang: 目标语言代码
            
        Returns:
            List[LLMTranslationResult]: 翻译结果列表
        """
        if not texts:
            return []
        
        # 过滤空文本
        valid_texts = []
        text_indices = []
        for i, text in enumerate(texts):
            if text.strip():
                valid_texts.append(text.strip())
                text_indices.append(i)
        
        if not valid_texts:
            # 所有文本都是空的
            return [
                LLMTranslationResult(
                    original_text=text,
                    translated_text=text,
                    success=True,
                    confidence=1.0
                ) for text in texts
            ]
        
        # 构建批量翻译提示词
        prompt = self._build_batch_translation_prompt(valid_texts, source_lang, target_lang)
        
        # 调试信息：输出发送的内容
        print(f"\n=== 批量翻译发送给大模型的内容 ===")
        print(prompt)
        print(f"=== 批量翻译发送内容结束 ===\n")
        
        # 重试机制
        for attempt in range(self.max_retries):
            try:
                response = self._make_api_request(prompt)
                
                # 调试信息：输出返回的内容
                print(f"\n=== 批量翻译大模型返回的内容 ===")
                print(f"完整响应: {response}")
                print(f"=== 批量翻译返回内容结束 ===\n")
                
                if response.get('error'):
                    error_msg = response['error'].get('message', '未知错误')
                    print(f"批量翻译API错误 (尝试 {attempt + 1}): {error_msg}")
                    if attempt == self.max_retries - 1:
                        # 最后一次失败，返回原文
                        return [
                            LLMTranslationResult(
                                original_text=text,
                                translated_text=text,
                                success=False,
                                error=f"API错误: {error_msg}",
                                confidence=0.0
                            ) for text in texts
                        ]
                    time.sleep(2 ** attempt)
                    continue
                
                # 解析批量翻译结果
                translated_texts = self._extract_batch_translation(response, len(valid_texts))
                
                # 构建结果列表
                results = []
                valid_index = 0
                
                for i, original_text in enumerate(texts):
                    if i in text_indices and valid_index < len(translated_texts):
                        # 有效文本，使用翻译结果
                        results.append(LLMTranslationResult(
                            original_text=original_text,
                            translated_text=translated_texts[valid_index],
                            success=True,
                            confidence=0.9
                        ))
                        valid_index += 1
                    else:
                        # 空文本，返回原文
                        results.append(LLMTranslationResult(
                            original_text=original_text,
                            translated_text=original_text,
                            success=True,
                            confidence=1.0
                        ))
                
                return results
                
            except Exception as e:
                print(f"批量翻译请求失败 (尝试 {attempt + 1}): {str(e)}")
                if attempt == self.max_retries - 1:
                    # 最后一次失败，返回原文
                    return [
                        LLMTranslationResult(
                            original_text=text,
                            translated_text=text,
                            success=False,
                            error=str(e),
                            confidence=0.0
                        ) for text in texts
                    ]
                time.sleep(2 ** attempt)
        
        # 不应该到达这里
        return [
            LLMTranslationResult(
                original_text=text,
                translated_text=text,
                success=False,
                error="批量翻译失败",
                confidence=0.0
            ) for text in texts
        ]
    
    def _extract_batch_translation(self, response: Dict[str, Any], expected_count: int) -> List[str]:
        """从批量翻译API响应中提取翻译结果"""
        try:
            choices = response.get('choices', [])
            if not choices:
                raise ValueError("API响应中没有choices")
            
            message = choices[0].get('message', {})
            content = message.get('content', '').strip()
            
            if not content:
                raise ValueError("API响应中没有内容")
            
            # 解析编号格式的翻译结果
            lines = content.split('\n')
            translations = []
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                
                # 匹配 "数字. 内容" 格式
                if '. ' in line:
                    parts = line.split('. ', 1)
                    if len(parts) == 2 and parts[0].isdigit():
                        translations.append(parts[1].strip())
                    else:
                        # 如果格式不对，直接使用整行
                        translations.append(line)
                else:
                    # 没有编号，直接使用
                    translations.append(line)
            
            # 确保翻译数量匹配
            if len(translations) != expected_count:
                print(f"警告: 期望 {expected_count} 个翻译，实际得到 {len(translations)} 个")
                # 补充或截断
                while len(translations) < expected_count:
                    translations.append("翻译失败")
                translations = translations[:expected_count]
            
            return translations
            
        except Exception as e:
            raise ValueError(f"解析批量翻译API响应失败: {str(e)}")
    
    def _build_translation_prompt(self, text: str, source_lang: str, target_lang: str) -> str:
        """构建翻译提示词"""
        lang_map = {
            'zh': '中文',
            'ja': '日文',
            'en': '英文'
        }
        
        source_name = lang_map.get(source_lang, source_lang)
        target_name = lang_map.get(target_lang, target_lang)
        
        # 计算原文字符数
        original_length = len(text.strip())
        max_length = int(original_length * 1.3)  # 最多比原文长30%
        
        prompt = f"""你是一个专业的电商翻译专家，请将以下电商产品图片中的{source_name}文本翻译成{target_name}。

背景：这些文字来自电商产品图片，包含产品名称、功能特点、宣传语等。

要求：
1. 保持原文的营销语调和产品卖点
2. 符合{target_name}电商表达习惯
3. 产品特性词汇要准确专业
4. 宣传语要自然流畅，有吸引力
5. 译文字符数量尽量和原文保持一致，最长不超过{max_length}个字符
6. 只返回翻译结果，不要添加任何解释

原文：{text}（{original_length}个字符）

翻译："""
        
        return prompt
    
    def _build_batch_translation_prompt(self, texts: list, source_lang: str, target_lang: str) -> str:
        """构建批量翻译提示词"""
        lang_map = {
            'zh': '中文',
            'ja': '日文',
            'en': '英文'
        }
        
        source_name = lang_map.get(source_lang, source_lang)
        target_name = lang_map.get(target_lang, target_lang)
        
        # 构建编号文本列表，并计算每个文本的字符数限制
        numbered_texts = []
        length_info = []
        for i, text in enumerate(texts, 1):
            original_length = len(text.strip())
            max_length = int(original_length * 1.3)  # 最多比原文长30%
            numbered_texts.append(f"{i}. {text}")
            length_info.append(f"第{i}项最多{max_length}字符")
        
        texts_content = "\n".join(numbered_texts)
        length_requirements = "，".join(length_info)
        
        prompt = f"""你是一个专业的电商翻译专家，请将以下电商产品图片中的{source_name}文本翻译成{target_name}。

背景：这些文字来自同一张电商产品图片，包含产品名称、功能特点、宣传语等。

要求：
1. 保持原文的营销语调和产品卖点
2. 符合{target_name}电商表达习惯
3. 产品特性词汇要准确专业
4. 宣传语要自然流畅，有吸引力
5. 翻译要考虑上下文语境的一致性
6. 译文字符数量尽量和原文保持一致，{length_requirements}
7. 按照原编号顺序返回翻译结果
8. 每行一个翻译，格式为：编号. 翻译内容

原文：
{texts_content}

翻译："""
        
        return prompt
    
    def _make_api_request(self, prompt: str) -> Dict[str, Any]:
        """发送API请求"""
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json',
            'HTTP-Referer': 'https://github.com/your-repo',  # OpenRouter要求
            'X-Title': 'Image Translation Tool'
        }
        
        data = {
            'model': self.model,
            'messages': [
                {
                    'role': 'user',
                    'content': prompt
                }
            ],
            'temperature': self.temperature,
            'max_tokens': self.max_tokens
        }
        
        response = requests.post(
            f'{self.base_url}/chat/completions',
            headers=headers,
            json=data,
            timeout=self.timeout
        )
        
        return response.json()
    
    def _extract_translation(self, response: Dict[str, Any]) -> str:
        """从API响应中提取翻译结果"""
        try:
            choices = response.get('choices', [])
            if not choices:
                raise ValueError("API响应中没有choices")
            
            message = choices[0].get('message', {})
            content = message.get('content', '').strip()
            
            if not content:
                raise ValueError("API响应中没有内容")
            
            return content
            
        except Exception as e:
            raise ValueError(f"解析API响应失败: {str(e)}")
    
    def health_check(self) -> bool:
        """检查模型服务是否可用"""
        try:
            test_result = self.translate("测试", "zh", "ja")
            return test_result.success
        except Exception as e:
            print(f"健康检查失败: {str(e)}")
            return False 