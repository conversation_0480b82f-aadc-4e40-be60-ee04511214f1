"""
翻译模型工厂
"""
from typing import Dict, Any
from .base import BaseTranslationModel
from .openai_model import OpenAITranslationModel


class TranslationModelFactory:
    """翻译模型工厂"""
    
    _models = {}
    _supported_providers = {
        'openai': OpenAITranslationModel,
    }
    
    @classmethod
    def create_model(cls, provider: str, config: Dict[str, Any]) -> BaseTranslationModel:
        """
        创建翻译模型实例
        
        Args:
            provider: 提供商名称 (openai, claude等)
            config: 模型配置
            
        Returns:
            BaseTranslationModel: 翻译模型实例
            
        Raises:
            ValueError: 不支持的提供商
        """
        if provider not in cls._supported_providers:
            raise ValueError(f"不支持的翻译模型提供商: {provider}. 支持的提供商: {list(cls._supported_providers.keys())}")
        
        # 使用单例模式，避免重复初始化
        model_key = f"{provider}_{hash(str(sorted(config.items())))}"
        
        if model_key not in cls._models:
            model_class = cls._supported_providers[provider]
            cls._models[model_key] = model_class(config)
            print(f"创建翻译模型: {provider} ({config.get('model', 'default')})")
        
        return cls._models[model_key]
    
    @classmethod
    def get_supported_providers(cls) -> list:
        """获取支持的提供商列表"""
        return list(cls._supported_providers.keys())
    
    @classmethod
    def clear_cache(cls):
        """清空模型缓存"""
        cls._models.clear() 