"""
可视化调试工具
负责生成各种调试图像
"""
import cv2
import numpy as np
import os
from typing import List
from PIL import Image, ImageDraw, ImageFont

from models.data_models import OCRResult, LayoutResult, TranslationResult
from config.settings import get_config_manager


class Visualizer:
    """可视化调试工具"""
    
    def __init__(self):
        """初始化可视化工具"""
        self.config_manager = get_config_manager()
        self._setup_fonts()
    
    def _setup_fonts(self):
        """设置调试用字体"""
        self.label_font = None
        try:
            import platform
            sys_name = platform.system()
            if sys_name == "Windows":
                self.label_font = ImageFont.truetype("C:/Windows/Fonts/simhei.ttf", 14)
            elif sys_name == "Darwin":
                self.label_font = ImageFont.truetype("/System/Library/Fonts/PingFang.ttc", 14)
            else:
                self.label_font = ImageFont.truetype("/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc", 14)
        except Exception:
            pass

        # 如果系统字体加载失败，尝试使用项目内字体
        if self.label_font is None:
            fonts_dir = self.config_manager.get_fonts_dir()
            project_fonts = {
                "思源黑体": "思源黑体/SourceHanSans-VF.otf",
                "台北黑体": "台北黑体/TaipeiSans-Bold.ttf",
                "NotoSans": "NotoSansSC/NotoSansSC-Black.ttf"
            }
            for font_name, font_path in project_fonts.items():
                full_path = os.path.join(fonts_dir, font_path)
                if os.path.exists(full_path):
                    try:
                        self.label_font = ImageFont.truetype(full_path, 14)
                        break
                    except Exception:
                        continue

        # 最后退回默认字体
        if self.label_font is None:
            try:
                self.label_font = ImageFont.load_default()
            except Exception:
                self.label_font = None
    
    def draw_ocr_detection(self, image_path: str, ocr_result: OCRResult, output_filename: str = "detection_simple.png"):
        """绘制OCR检测结果"""
        try:
            image = cv2.imread(image_path)
            if image is None:
                print(f"无法读取图像: {image_path}")
                return
            
            # 绘制所有检测框
            for i, (poly, text, score) in enumerate(zip(ocr_result.dt_polys, ocr_result.rec_texts, ocr_result.rec_scores)):
                points = np.array(poly, dtype=np.int32)
                
                # 判断是否为中文文字
                is_chinese = any(region.text == text for region in ocr_result.chinese_regions)
                box_color = (0, 0, 255) if is_chinese else (0, 255, 0)  # 中文用红色，其他用绿色
                
                # 绘制检测框
                cv2.polylines(image, [points], True, box_color, 2)
                
                # 添加序号
                x, y = points[0]
                label = f"{i+1}"
                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
                cv2.rectangle(image, (x, y-25), (x + label_size[0] + 8, y), box_color, -1)
                cv2.putText(image, label, (x + 4, y - 6), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            
            # 保存结果到OCR调试目录
            debug_dir = self.config_manager.config.ocr_debug_dir
            os.makedirs(debug_dir, exist_ok=True)
            output_path = os.path.join(debug_dir, output_filename)
            cv2.imwrite(output_path, image)
            print(f"OCR检测结果已保存: {output_path}")
            
        except Exception as e:
            print(f"绘制OCR检测结果失败: {e}")
    
    def draw_layout_analysis(self, image_path: str, layout_result: LayoutResult, output_filename: str = "layout_analysis_debug.png"):
        """绘制布局分析调试图"""
        try:
            image = cv2.imread(image_path)
            if image is None:
                print(f"无法读取图像: {image_path}")
                return
            
            debug_image = image.copy()
            
            # 转换为PIL图像进行中文标签绘制
            pil_image = Image.fromarray(cv2.cvtColor(debug_image, cv2.COLOR_BGR2RGB))
            draw = ImageDraw.Draw(pil_image)
            
            # 绘制文字区域和编号
            regions = layout_result.regions
            for i, region in enumerate(regions):
                x, y, w, h = region['bbox']
                
                # 绘制区域框
                cv2.rectangle(debug_image, (x, y), (x + w, y + h), (0, 255, 255), 2)
                
                # 绘制中心点
                center_x, center_y = region['center']
                cv2.circle(debug_image, (center_x, center_y), 4, (255, 0, 255), -1)
                
                # 使用Pillow绘制文字标签
                if self.label_font:
                    try:
                        label_text = f"{i+1}: {region['text']}"
                        draw.text((x, y - 25), label_text, fill=(255, 255, 0), font=self.label_font)
                    except:
                        draw.text((x, y - 25), f"{i+1}", fill=(255, 255, 0))
            
            # 绘制对齐线
            self._draw_alignment_lines(debug_image, layout_result.horizontal_alignment)
            
            # 更新PIL图像
            pil_image = Image.fromarray(cv2.cvtColor(debug_image, cv2.COLOR_BGR2RGB))
            draw = ImageDraw.Draw(pil_image)
            
            # 添加信息面板
            self._add_layout_info_panel(draw, layout_result)
            
            # 转换回OpenCV格式并保存到layout调试目录
            final_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
            debug_dir = self.config_manager.config.layout_debug_dir
            os.makedirs(debug_dir, exist_ok=True)
            output_path = os.path.join(debug_dir, output_filename)
            cv2.imwrite(output_path, final_image)
            print(f"布局分析调试图已保存: {output_path}")
            
        except Exception as e:
            print(f"绘制布局分析调试图失败: {e}")
    

    
    def _draw_alignment_lines(self, image: np.ndarray, h_align: dict):
        """绘制对齐线"""
        # 绘制左对齐线（绿色）
        if h_align.get('left_groups'):
            for group in h_align['left_groups']:
                if len(group) >= 2:
                    left_x = group[0]['left']
                    min_y = min(r['top'] for r in group) - 10
                    max_y = max(r['bottom'] for r in group) + 10
                    cv2.line(image, (left_x, min_y), (left_x, max_y), (0, 255, 0), 3)
        
        # 绘制居中对齐线（红色）
        if h_align.get('center_groups'):
            for group in h_align['center_groups']:
                if len(group) >= 2:
                    center_x = group[0]['center'][0]
                    min_y = min(r['top'] for r in group) - 10
                    max_y = max(r['bottom'] for r in group) + 10
                    cv2.line(image, (center_x, min_y), (center_x, max_y), (255, 0, 0), 3)
        
        # 绘制右对齐线（蓝色）
        if h_align.get('right_groups'):
            for group in h_align['right_groups']:
                if len(group) >= 2:
                    right_x = group[0]['right']
                    min_y = min(r['top'] for r in group) - 10
                    max_y = max(r['bottom'] for r in group) + 10
                    cv2.line(image, (right_x, min_y), (right_x, max_y), (255, 255, 0), 3)
    
    def _add_layout_info_panel(self, draw: ImageDraw.Draw, layout_result: LayoutResult):
        """添加布局信息面板"""
        if not self.label_font:
            return
        
        info_panel_height = 160
        info_panel = Image.new('RGBA', (450, info_panel_height), (0, 0, 0, 180))
        
        try:
            y_offset = 20
            draw.text((20, y_offset), f"布局分析结果", fill=(255, 255, 255), font=self.label_font)
            y_offset += 20
            
            draw.text((20, y_offset), f"布局模式: {layout_result.layout_mode}", fill=(255, 255, 255), font=self.label_font)
            y_offset += 20
            
            draw.text((20, y_offset), f"文字区域: {len(layout_result.regions)}个", fill=(255, 255, 255), font=self.label_font)
            y_offset += 20
            
            h_align = layout_result.horizontal_alignment
            draw.text((20, y_offset), f"水平对齐: {h_align['type']}", fill=(255, 255, 255), font=self.label_font)
            
        except Exception as e:
            print(f"添加布局信息面板失败: {e}")
    

