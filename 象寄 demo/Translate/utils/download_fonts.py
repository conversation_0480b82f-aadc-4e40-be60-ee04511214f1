import os
import urllib.request
import shutil
from pathlib import Path

def download_font(url, filename):
    """下载单个字体文件"""
    fonts_dir = Path("fonts")
    fonts_dir.mkdir(exist_ok=True)
    
    filepath = fonts_dir / filename
    
    if filepath.exists():
        print(f"✓ 字体已存在: {filename}")
        return True
    
    try:
        print(f"下载字体: {filename}...")
        
        # 设置User-Agent避免被阻止
        req = urllib.request.Request(url)
        req.add_header('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
        
        with urllib.request.urlopen(req, timeout=60) as response:
            with open(filepath, 'wb') as f:
                shutil.copyfileobj(response, f)
        
        print(f"✓ 下载成功: {filename}")
        return True
        
    except Exception as e:
        print(f"✗ 下载失败 {filename}: {e}")
        return False

def main():
    """下载推荐的开源字体"""
    print("=== 开源字体下载器 ===\n")
    
    # 推荐下载的字体（使用更可靠的源）
    fonts_to_download = [
        {
            "name": "Noto Sans CJK SC Regular",
            "filename": "NotoSansCJKsc-Regular.otf",
            "url": "https://github.com/googlefonts/noto-cjk/releases/download/Sans2.004/04_NotoSansCJKsc.zip",
            "description": "Google开源字体，支持中日韩文字"
        },
        {
            "name": "Noto Serif CJK SC Regular", 
            "filename": "NotoSerifCJKsc-Regular.otf",
            "url": "https://github.com/googlefonts/noto-cjk/releases/download/Serif1.001/05_NotoSerifCJKsc.zip",
            "description": "Google开源衬线字体"
        }
    ]
    
    print("由于网络限制，我们提供手动下载指南：\n")
    
    print("📥 推荐下载的高质量开源字体：\n")
    
    print("1. 思源黑体 (Source Han Sans)")
    print("   - 下载地址: https://github.com/adobe-fonts/source-han-sans/releases")
    print("   - 选择: SourceHanSans.ttc (包含所有语言)")
    print("   - 文件大小: ~17MB")
    print("   - 保存到: fonts/SourceHanSans.ttc")
    print()
    
    print("2. 思源宋体 (Source Han Serif)")
    print("   - 下载地址: https://github.com/adobe-fonts/source-han-serif/releases")
    print("   - 选择: SourceHanSerif.ttc (包含所有语言)")
    print("   - 文件大小: ~19MB")
    print("   - 保存到: fonts/SourceHanSerif.ttc")
    print()
    
    print("3. Noto Sans CJK")
    print("   - 下载地址: https://github.com/googlefonts/noto-cjk/releases")
    print("   - 选择: NotoSansCJK.ttc (包含所有语言)")
    print("   - 文件大小: ~16MB")
    print("   - 保存到: fonts/NotoSansCJK.ttc")
    print()
    
    print("4. 文泉驿微米黑")
    print("   - 下载地址: http://wenq.org/wqy2/index.cgi?MicroHei")
    print("   - 选择: wqy-microhei.ttc")
    print("   - 文件大小: ~4MB")
    print("   - 保存到: fonts/wqy-microhei.ttc")
    print()
    
    print("🔧 下载完成后的设置：")
    print("1. 将下载的字体文件放到 fonts/ 目录")
    print("2. 重新运行 python demo.py 测试")
    print("3. 程序会自动检测并使用新字体")
    print()
    
    # 检查已有字体
    fonts_dir = Path("fonts")
    if fonts_dir.exists():
        existing_fonts = list(fonts_dir.glob("*.ttf")) + list(fonts_dir.glob("*.ttc")) + list(fonts_dir.glob("*.otf"))
        if existing_fonts:
            print("📂 已有字体文件：")
            for font in existing_fonts:
                size_mb = font.stat().st_size / (1024 * 1024)
                print(f"  - {font.name} ({size_mb:.1f} MB)")
        else:
            print("📂 fonts/ 目录为空，请下载字体文件")
    else:
        print("📂 请先创建 fonts/ 目录")

if __name__ == "__main__":
    main() 