from font_scanner import SystemFontScanner
from font_downloader import OpenSourceFontDownloader
import json
import os
from pathlib import Path

class EnhancedFontManager:
    def __init__(self):
        self.scanner = SystemFontScanner()
        self.downloader = OpenSourceFontDownloader()
        self.font_database = {}
        self.load_font_database()
    
    def load_font_database(self):
        """加载字体数据库"""
        # 加载系统字体
        if os.path.exists("system_fonts.json"):
            with open("system_fonts.json", 'r', encoding='utf-8') as f:
                system_fonts = json.load(f)
            self.font_database.update(system_fonts)
        
        # 加载下载的开源字体
        downloaded_fonts = self.downloader.get_downloaded_fonts()
        for font_key, files in downloaded_fonts.items():
            for file_path in files:
                file_name = os.path.basename(file_path)
                self.font_database[file_name] = {
                    'path': file_path,
                    'source': 'downloaded',
                    'font_family': font_key,
                    'chinese_support': True,
                    'japanese_support': True,
                    'english_support': True,
                    'recommended': True
                }
    
    def get_best_fonts_for_translation(self):
        """获取最适合翻译的字体"""
        best_fonts = {}
        
        # 系统字体中的推荐字体
        system_recommended = [
            'msyh.ttc',      # 微软雅黑
            'msyhbd.ttc',    # 微软雅黑粗体
            'simhei.ttf',    # 黑体
            'simkai.ttf',    # 楷体
            'simsun.ttc',    # 宋体
            'msgothic.ttc',  # MS Gothic (日文)
            'msjh.ttc',      # Microsoft JhengHei (繁体中文)
        ]
        
        for font_name in system_recommended:
            if font_name in self.font_database:
                font_info = self.font_database[font_name]
                if font_info.get('recommended', False):
                    best_fonts[font_name] = font_info
        
        # 下载的开源字体
        downloaded_fonts = self.downloader.get_downloaded_fonts()
        for font_key, files in downloaded_fonts.items():
            for file_path in files:
                file_name = os.path.basename(file_path)
                best_fonts[f"{font_key}_{file_name}"] = {
                    'path': file_path,
                    'source': 'downloaded',
                    'font_family': font_key,
                    'recommended': True
                }
        
        return best_fonts
    
    def get_font_by_style(self, style_type):
        """根据字体风格获取字体"""
        style_mapping = {
            'serif': ['simsun.ttc', 'times.ttf', 'SourceHanSerifCN-Regular.otf'],
            'sans_serif': ['msyh.ttc', 'arial.ttf', 'SourceHanSansCN-Regular.otf'],
            'bold': ['msyhbd.ttc', 'arialbd.ttf', 'simhei.ttf'],
            'script': ['simkai.ttf', 'brush_script.ttf'],
            'monospace': ['consola.ttf', 'cour.ttf']
        }
        
        recommended_fonts = []
        for font_name in style_mapping.get(style_type, []):
            if font_name in self.font_database:
                recommended_fonts.append({
                    'name': font_name,
                    'path': self.font_database[font_name]['path'],
                    'info': self.font_database[font_name]
                })
        
        return recommended_fonts
    
    def create_font_mapping_for_translator(self):
        """为翻译器创建字体映射"""
        font_mapping = {}
        
        # 基于最佳字体创建映射
        best_fonts = self.get_best_fonts_for_translation()
        
        # 中文字体映射
        chinese_fonts = {
            '宋体': self.find_best_font(['simsun.ttc', 'SourceHanSerifCN-Regular.otf']),
            '黑体': self.find_best_font(['simhei.ttf', 'SourceHanSansCN-Bold.otf']),
            '楷体': self.find_best_font(['simkai.ttf', 'SourceHanSerifCN-Regular.otf']),
            '微软雅黑': self.find_best_font(['msyh.ttc', 'SourceHanSansCN-Regular.otf']),
            '仿宋': self.find_best_font(['simfang.ttf', 'SourceHanSerifCN-Regular.otf']),
            '隶书': self.find_best_font(['SIMLI.TTF', 'SourceHanSerifCN-Bold.otf']),
            '幼圆': self.find_best_font(['SIMYOU.TTF', 'SourceHanSansCN-Regular.otf'])
        }
        
        # 添加系统中发现的其他中文字体
        for font_name, info in self.font_database.items():
            if (info.get('chinese_support') and 
                info.get('japanese_support') and 
                info.get('recommended')):
                
                # 根据字体名称推断类型
                if 'kai' in font_name.lower() or '楷' in font_name:
                    chinese_fonts['楷体'] = info['path']
                elif 'hei' in font_name.lower() or '黑' in font_name:
                    chinese_fonts['黑体'] = info['path']
                elif 'song' in font_name.lower() or '宋' in font_name:
                    chinese_fonts['宋体'] = info['path']
                elif 'yahei' in font_name.lower() or '雅黑' in font_name:
                    chinese_fonts['微软雅黑'] = info['path']
        
        return chinese_fonts
    
    def find_best_font(self, preferred_fonts):
        """在首选字体列表中找到最佳可用字体"""
        for font_name in preferred_fonts:
            if font_name in self.font_database:
                return self.font_database[font_name]['path']
        
        # 如果首选字体都不可用，返回任意可用的中日文字体
        for font_name, info in self.font_database.items():
            if (info.get('chinese_support') and 
                info.get('japanese_support')):
                return info['path']
        
        return None
    
    def update_translator_fonts(self, translator_file='translator.py'):
        """更新translator.py中的字体映射"""
        font_mapping = self.create_font_mapping_for_translator()
        
        print("推荐的字体映射:")
        for font_type, font_path in font_mapping.items():
            if font_path:
                print(f"  {font_type}: {font_path}")
            else:
                print(f"  {font_type}: 未找到合适字体")
        
        # 这里可以添加自动更新translator.py的代码
        # 或者生成配置文件供手动更新
        
        with open('recommended_font_mapping.json', 'w', encoding='utf-8') as f:
            json.dump(font_mapping, f, ensure_ascii=False, indent=2)
        
        print("\n字体映射已保存到: recommended_font_mapping.json")
    
    def generate_font_report(self):
        """生成字体报告"""
        total_fonts = len(self.font_database)
        recommended_fonts = sum(1 for info in self.font_database.values() 
                               if info.get('recommended', False))
        
        chinese_support = sum(1 for info in self.font_database.values() 
                             if info.get('chinese_support', False))
        japanese_support = sum(1 for info in self.font_database.values() 
                              if info.get('japanese_support', False))
        
        print("=== 字体管理报告 ===")
        print(f"总字体数量: {total_fonts}")
        print(f"推荐字体: {recommended_fonts}")
        print(f"支持中文: {chinese_support}")
        print(f"支持日文: {japanese_support}")
        
        print("\n=== 最佳翻译字体 ===")
        best_fonts = self.get_best_fonts_for_translation()
        for font_name, info in best_fonts.items():
            source = info.get('source', 'system')
            print(f"  {font_name} ({source})")
        
        print(f"\n共找到 {len(best_fonts)} 个适合翻译的字体")

if __name__ == "__main__":
    print("=== 增强字体管理器 ===\n")
    
    manager = EnhancedFontManager()
    
    while True:
        print("\n选择操作:")
        print("1. 扫描系统字体")
        print("2. 查看开源字体")
        print("3. 下载推荐字体")
        print("4. 生成字体报告")
        print("5. 更新翻译器字体映射")
        print("6. 退出")
        
        choice = input("\n请输入选择 (1-6): ").strip()
        
        if choice == "1":
            print("扫描系统字体...")
            fonts = manager.scanner.scan_all_fonts()
            manager.scanner.save_font_database(fonts)
            manager.load_font_database()  # 重新加载
            print(f"扫描完成，找到 {len(fonts)} 个字体")
        
        elif choice == "2":
            manager.downloader.list_available_fonts()
        
        elif choice == "3":
            print("下载推荐的开源字体...")
            # 下载思源黑体和Noto Sans CJK
            manager.downloader.download_font("思源黑体")
            manager.downloader.download_font("Noto Sans CJK")
            manager.load_font_database()  # 重新加载
        
        elif choice == "4":
            manager.generate_font_report()
        
        elif choice == "5":
            manager.update_translator_fonts()
        
        elif choice == "6":
            print("再见!")
            break
        
        else:
            print("无效的选择，请重试") 