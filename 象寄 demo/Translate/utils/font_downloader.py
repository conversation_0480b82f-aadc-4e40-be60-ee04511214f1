import os
import requests
import zipfile
import shutil
from pathlib import Path
import json

class OpenSourceFontDownloader:
    def __init__(self):
        self.fonts_dir = Path("fonts")
        self.fonts_dir.mkdir(exist_ok=True)
        
        # 开源字体资源库
        self.font_sources = {
            "思源黑体": {
                "name": "Source Han Sans",
                "description": "Adobe开源无衬线字体，支持中日韩",
                "files": {
                    "SourceHanSansCN-Regular.otf": "https://github.com/adobe-fonts/source-han-sans/raw/release/OTF/SimplifiedChinese/SourceHanSansCN-Regular.otf",
                    "SourceHanSansCN-Bold.otf": "https://github.com/adobe-fonts/source-han-sans/raw/release/OTF/SimplifiedChinese/SourceHanSansCN-Bold.otf",
                    "SourceHanSansJP-Regular.otf": "https://github.com/adobe-fonts/source-han-sans/raw/release/OTF/Japanese/SourceHanSansJP-Regular.otf",
                    "SourceHanSansJP-Bold.otf": "https://github.com/adobe-fonts/source-han-sans/raw/release/OTF/Japanese/SourceHanSansJP-Bold.otf"
                },
                "license": "SIL Open Font License",
                "commercial_use": True
            },
            
            "思源宋体": {
                "name": "Source Han Serif", 
                "description": "Adobe开源衬线字体，支持中日韩",
                "files": {
                    "SourceHanSerifCN-Regular.otf": "https://github.com/adobe-fonts/source-han-serif/raw/release/OTF/SimplifiedChinese/SourceHanSerifCN-Regular.otf",
                    "SourceHanSerifCN-Bold.otf": "https://github.com/adobe-fonts/source-han-serif/raw/release/OTF/SimplifiedChinese/SourceHanSerifCN-Bold.otf",
                    "SourceHanSerifJP-Regular.otf": "https://github.com/adobe-fonts/source-han-serif/raw/release/OTF/Japanese/SourceHanSerifJP-Regular.otf",
                    "SourceHanSerifJP-Bold.otf": "https://github.com/adobe-fonts/source-han-serif/raw/release/OTF/Japanese/SourceHanSerifJP-Bold.otf"
                },
                "license": "SIL Open Font License",
                "commercial_use": True
            },
            
            "Noto Sans CJK": {
                "name": "Noto Sans CJK",
                "description": "Google开源字体，完美支持中日韩文字",
                "files": {
                    "NotoSansCJKsc-Regular.otf": "https://github.com/googlefonts/noto-cjk/raw/main/Sans/OTF/SimplifiedChinese/NotoSansCJKsc-Regular.otf",
                    "NotoSansCJKsc-Bold.otf": "https://github.com/googlefonts/noto-cjk/raw/main/Sans/OTF/SimplifiedChinese/NotoSansCJKsc-Bold.otf",
                    "NotoSansCJKjp-Regular.otf": "https://github.com/googlefonts/noto-cjk/raw/main/Sans/OTF/Japanese/NotoSansCJKjp-Regular.otf",
                    "NotoSansCJKjp-Bold.otf": "https://github.com/googlefonts/noto-cjk/raw/main/Sans/OTF/Japanese/NotoSansCJKjp-Bold.otf"
                },
                "license": "SIL Open Font License",
                "commercial_use": True
            },
            
            "文泉驿微米黑": {
                "name": "WenQuanYi Micro Hei",
                "description": "开源中文字体，轻量级",
                "files": {
                    "wqy-microhei.ttc": "https://github.com/anthonyfok/fonts-wqy-microhei/raw/master/wqy-microhei.ttc"
                },
                "license": "Apache License 2.0",
                "commercial_use": True
            },
            
            "站酷文艺体": {
                "name": "ZCOOL Wenyiti",
                "description": "站酷开源文艺字体",
                "files": {
                    "ZCOOLWenYi-Regular.ttf": "https://github.com/googlefonts/google-fonts/raw/main/ofl/zcoolwenyi/ZCOOLWenYi-Regular.ttf"
                },
                "license": "SIL Open Font License",
                "commercial_use": True
            }
        }
    
    def download_font(self, font_key, file_name=None):
        """下载指定字体"""
        if font_key not in self.font_sources:
            print(f"字体 {font_key} 不存在")
            return False
        
        font_info = self.font_sources[font_key]
        font_dir = self.fonts_dir / font_key
        font_dir.mkdir(exist_ok=True)
        
        files_to_download = font_info["files"]
        if file_name:
            if file_name not in files_to_download:
                print(f"文件 {file_name} 不存在于 {font_key}")
                return False
            files_to_download = {file_name: files_to_download[file_name]}
        
        success_count = 0
        for filename, url in files_to_download.items():
            local_path = font_dir / filename
            
            if local_path.exists():
                print(f"文件已存在: {local_path}")
                success_count += 1
                continue
            
            try:
                print(f"下载: {filename}...")
                response = requests.get(url, stream=True, timeout=30)
                response.raise_for_status()
                
                with open(local_path, 'wb') as f:
                    shutil.copyfileobj(response.raw, f)
                
                print(f"✓ 下载完成: {local_path}")
                success_count += 1
                
            except Exception as e:
                print(f"✗ 下载失败 {filename}: {e}")
        
        # 保存字体信息
        info_file = font_dir / "font_info.json"
        with open(info_file, 'w', encoding='utf-8') as f:
            json.dump(font_info, f, ensure_ascii=False, indent=2)
        
        print(f"字体 {font_key} 下载完成: {success_count}/{len(font_info['files'])} 个文件")
        return success_count > 0
    
    def download_all_fonts(self):
        """下载所有推荐字体"""
        print("开始下载所有开源字体...")
        
        for font_key in self.font_sources:
            print(f"\n--- 下载 {font_key} ---")
            self.download_font(font_key)
        
        print("\n所有字体下载完成！")
    
    def list_available_fonts(self):
        """列出所有可用的开源字体"""
        print("可用的开源字体:")
        print("=" * 50)
        
        for key, info in self.font_sources.items():
            print(f"\n📚 {key} ({info['name']})")
            print(f"   描述: {info['description']}")
            print(f"   许可证: {info['license']}")
            print(f"   商用: {'✓' if info['commercial_use'] else '✗'}")
            print(f"   文件数量: {len(info['files'])}")
            
            # 检查本地是否已下载
            font_dir = self.fonts_dir / key
            if font_dir.exists():
                downloaded_files = list(font_dir.glob("*.otf")) + list(font_dir.glob("*.ttf")) + list(font_dir.glob("*.ttc"))
                print(f"   本地状态: 已下载 {len(downloaded_files)} 个文件")
            else:
                print(f"   本地状态: 未下载")
    
    def get_downloaded_fonts(self):
        """获取已下载的字体列表"""
        downloaded = {}
        
        for font_key in self.font_sources:
            font_dir = self.fonts_dir / font_key
            if font_dir.exists():
                font_files = []
                for ext in ['*.otf', '*.ttf', '*.ttc']:
                    font_files.extend(font_dir.glob(ext))
                
                if font_files:
                    downloaded[font_key] = [str(f) for f in font_files]
        
        return downloaded
    
    def install_to_system(self, font_key):
        """安装字体到系统（需要管理员权限）"""
        if font_key not in self.font_sources:
            print(f"字体 {font_key} 不存在")
            return False
        
        font_dir = self.fonts_dir / font_key
        if not font_dir.exists():
            print(f"字体 {font_key} 尚未下载，请先下载")
            return False
        
        system_font_dir = Path("C:/Windows/Fonts/")
        if not system_font_dir.exists():
            print("无法找到系统字体目录")
            return False
        
        font_files = list(font_dir.glob("*.otf")) + list(font_dir.glob("*.ttf")) + list(font_dir.glob("*.ttc"))
        
        installed_count = 0
        for font_file in font_files:
            try:
                target_path = system_font_dir / font_file.name
                if not target_path.exists():
                    shutil.copy2(font_file, target_path)
                    print(f"✓ 已安装: {font_file.name}")
                    installed_count += 1
                else:
                    print(f"已存在: {font_file.name}")
                    installed_count += 1
            except PermissionError:
                print(f"✗ 权限不足，无法安装: {font_file.name}")
                print("请以管理员身份运行程序")
            except Exception as e:
                print(f"✗ 安装失败 {font_file.name}: {e}")
        
        return installed_count > 0

if __name__ == "__main__":
    downloader = OpenSourceFontDownloader()
    
    print("=== 开源字体下载器 ===\n")
    
    while True:
        print("\n选择操作:")
        print("1. 查看可用字体")
        print("2. 下载指定字体")
        print("3. 下载所有字体")
        print("4. 查看已下载字体")
        print("5. 退出")
        
        choice = input("\n请输入选择 (1-5): ").strip()
        
        if choice == "1":
            downloader.list_available_fonts()
        
        elif choice == "2":
            print("\n可选字体:")
            for i, key in enumerate(downloader.font_sources.keys(), 1):
                print(f"{i}. {key}")
            
            try:
                font_idx = int(input("\n请选择字体编号: ")) - 1
                font_keys = list(downloader.font_sources.keys())
                if 0 <= font_idx < len(font_keys):
                    downloader.download_font(font_keys[font_idx])
                else:
                    print("无效的选择")
            except ValueError:
                print("请输入有效的数字")
        
        elif choice == "3":
            downloader.download_all_fonts()
        
        elif choice == "4":
            downloaded = downloader.get_downloaded_fonts()
            if downloaded:
                print("\n已下载的字体:")
                for font_key, files in downloaded.items():
                    print(f"\n{font_key}:")
                    for file in files:
                        print(f"  - {file}")
            else:
                print("\n暂无已下载的字体")
        
        elif choice == "5":
            print("再见!")
            break
        
        else:
            print("无效的选择，请重试") 