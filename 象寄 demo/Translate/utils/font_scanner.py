import os
import platform
from PIL import Image, ImageFont, ImageDraw
import json

class SystemFontScanner:
    def __init__(self):
        self.system_type = platform.system()
        self.font_paths = self.get_system_font_paths()
        
    def get_system_font_paths(self):
        """获取系统字体路径"""
        if self.system_type == "Windows":
            return [
                "C:/Windows/Fonts/",
                "C:/Windows/System32/Fonts/",
                os.path.expanduser("~/AppData/Local/Microsoft/Windows/Fonts/"),
                os.path.expanduser("~/AppData/Roaming/Microsoft/Windows/Fonts/")
            ]
        elif self.system_type == "Darwin":  # macOS
            return [
                "/System/Library/Fonts/",
                "/Library/Fonts/",
                os.path.expanduser("~/Library/Fonts/"),
                "/System/Library/Assets/com_apple_MobileAsset_Font6/"
            ]
        elif self.system_type == "Linux":
            return [
                "/usr/share/fonts/",
                "/usr/local/share/fonts/",
                os.path.expanduser("~/.fonts/"),
                os.path.expanduser("~/.local/share/fonts/")
            ]
        return []
    
    def scan_all_fonts(self):
        """扫描所有可用字体"""
        found_fonts = {}
        
        for font_dir in self.font_paths:
            if os.path.exists(font_dir):
                print(f"扫描目录: {font_dir}")
                for root, dirs, files in os.walk(font_dir):
                    for file in files:
                        if file.lower().endswith(('.ttf', '.ttc', '.otf')):
                            font_path = os.path.join(root, file)
                            font_info = self.analyze_font(font_path)
                            if font_info:
                                found_fonts[file] = font_info
        
        return found_fonts
    
    def analyze_font(self, font_path):
        """分析字体信息"""
        try:
            font = ImageFont.truetype(font_path, 20)
            
            # 测试中文支持
            chinese_support = self.test_chinese_support(font)
            # 测试日文支持  
            japanese_support = self.test_japanese_support(font)
            # 测试英文支持
            english_support = self.test_english_support(font)
            
            return {
                'path': font_path,
                'chinese_support': chinese_support,
                'japanese_support': japanese_support,
                'english_support': english_support,
                'file_size': os.path.getsize(font_path),
                'recommended': chinese_support and japanese_support
            }
        except Exception as e:
            print(f"无法分析字体 {font_path}: {e}")
            return None
    
    def test_chinese_support(self, font):
        """测试中文字符支持"""
        test_chars = ['中', '文', '测', '试', '字', '体']
        return self.test_character_support(font, test_chars)
    
    def test_japanese_support(self, font):
        """测试日文字符支持"""
        test_chars = ['あ', 'か', 'さ', 'サ', 'ポ', 'ン', '大', '切']
        return self.test_character_support(font, test_chars)
    
    def test_english_support(self, font):
        """测试英文字符支持"""
        test_chars = ['A', 'B', 'C', 'a', 'b', 'c', '1', '2', '3']
        return self.test_character_support(font, test_chars)
    
    def test_character_support(self, font, test_chars):
        """测试字符支持"""
        try:
            supported_count = 0
            for char in test_chars:
                bbox = font.getbbox(char)
                width = bbox[2] - bbox[0]
                if width > 5:  # 字符宽度大于5像素认为支持
                    supported_count += 1
            
            support_ratio = supported_count / len(test_chars)
            return support_ratio > 0.8  # 80%以上字符支持则认为支持该语言
        except:
            return False
    
    def save_font_database(self, fonts_info, filename="system_fonts.json"):
        """保存字体数据库"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(fonts_info, f, ensure_ascii=False, indent=2)
        print(f"字体数据库已保存到: {filename}")
    
    def generate_font_preview(self, font_path, output_path="font_preview.png"):
        """生成字体预览图"""
        try:
            font_sizes = [16, 20, 24, 32]
            test_text = "中文 Japanese 日本語 English"
            
            img_width, img_height = 800, 200
            img = Image.new('RGB', (img_width, img_height), 'white')
            draw = ImageDraw.Draw(img)
            
            y_offset = 10
            for size in font_sizes:
                try:
                    font = ImageFont.truetype(font_path, size)
                    draw.text((10, y_offset), f"{size}px: {test_text}", font=font, fill='black')
                    y_offset += size + 10
                except:
                    continue
            
            img.save(output_path)
            print(f"字体预览已保存到: {output_path}")
            return True
        except Exception as e:
            print(f"生成预览失败: {e}")
            return False

if __name__ == "__main__":
    scanner = SystemFontScanner()
    print("开始扫描系统字体...")
    
    fonts = scanner.scan_all_fonts()
    
    print(f"\n找到 {len(fonts)} 个字体文件")
    
    # 过滤推荐字体
    recommended_fonts = {k: v for k, v in fonts.items() if v['recommended']}
    print(f"推荐字体 {len(recommended_fonts)} 个:")
    
    for font_name, info in recommended_fonts.items():
        print(f"  {font_name}: {info['path']}")
    
    # 保存字体数据库
    scanner.save_font_database(fonts)
    
    print("\n扫描完成！") 