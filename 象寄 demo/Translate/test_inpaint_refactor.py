#!/usr/bin/env python3
"""
测试重构后的InpaintProcessor功能
验证mask_inpaint模块集成和配置传递
"""
import cv2
import numpy as np
import os
import sys
from typing import List

# 添加项目根目录到路径
sys.path.append(os.path.dirname(__file__))

from processors.inpaint_processor import InpaintProcessor
from models.data_models import TextRegion
from config.settings import get_config_manager
from mask_inpaint.factory import InpaintModelFactory


def create_test_image_and_regions():
    """创建测试图像和文字区域"""
    # 创建一个简单的测试图像
    image = np.ones((300, 400, 3), dtype=np.uint8) * 255  # 白色背景
    
    # 添加一些文字区域（模拟中文文字）
    cv2.rectangle(image, (50, 50), (150, 100), (0, 0, 0), -1)  # 黑色矩形
    cv2.rectangle(image, (200, 150), (350, 200), (0, 0, 0), -1)  # 另一个黑色矩形
    
    # 创建对应的TextRegion
    regions = [
        TextRegion(
            id=1,
            text="测试文字1",
            bbox=(50, 50, 100, 50),
            poly=np.array([[50, 50], [150, 50], [150, 100], [50, 100]]),
            score=0.9,
            is_chinese=True,
            center=(100, 75)
        ),
        TextRegion(
            id=2,
            text="测试文字2",
            bbox=(200, 150, 150, 50),
            poly=np.array([[200, 150], [350, 150], [350, 200], [200, 200]]),
            score=0.85,
            is_chinese=True,
            center=(275, 175)
        )
    ]
    
    return image, regions


def test_inpaint_processor_initialization():
    """测试InpaintProcessor初始化"""
    print("=== 测试InpaintProcessor初始化 ===")
    
    try:
        # 测试默认初始化
        processor = InpaintProcessor()
        print(f"✓ 默认初始化成功，引擎: {processor.engine_name}")
        
        # 测试指定引擎初始化
        processor_opencv = InpaintProcessor(engine='opencv', config={'method': 'telea'})
        print(f"✓ OpenCV引擎初始化成功，引擎: {processor_opencv.engine_name}")
        
        return True
        
    except Exception as e:
        print(f"✗ 初始化失败: {str(e)}")
        return False


def test_mask_creation():
    """测试掩码创建功能"""
    print("\n=== 测试掩码创建 ===")
    
    try:
        image, regions = create_test_image_and_regions()
        processor = InpaintProcessor()
        
        # 创建掩码
        mask = processor._create_text_mask(image, regions)
        
        # 验证掩码
        assert mask.shape == image.shape[:2], "掩码尺寸不匹配"
        assert mask.dtype == np.uint8, "掩码类型不正确"
        assert np.max(mask) == 255, "掩码值不正确"
        
        print(f"✓ 掩码创建成功，尺寸: {mask.shape}")
        return True
        
    except Exception as e:
        print(f"✗ 掩码创建失败: {str(e)}")
        return False


def test_inpainting_process():
    """测试图像修复过程"""
    print("\n=== 测试图像修复过程 ===")
    
    try:
        image, regions = create_test_image_and_regions()
        processor = InpaintProcessor()
        
        # 执行图像修复
        result = processor.process_inpainting(image, regions)
        
        # 验证结果
        assert result.success, f"修复失败: {result.error_message}"
        assert result.data is not None, "修复结果为空"
        assert result.data.shape == image.shape, "修复后图像尺寸不匹配"
        
        print("✓ 图像修复成功")
        return True
        
    except Exception as e:
        print(f"✗ 图像修复失败: {str(e)}")
        return False


def test_different_engines():
    """测试不同的修复引擎"""
    print("\n=== 测试不同修复引擎 ===")
    
    engines_to_test = ['opencv', 'lama']
    results = {}
    
    for engine in engines_to_test:
        try:
            print(f"测试引擎: {engine}")
            
            # 创建处理器
            config = {'device': 'cpu', 'quality': 'medium'}
            processor = InpaintProcessor(engine=engine, config=config)
            
            # 测试修复
            image, regions = create_test_image_and_regions()
            result = processor.process_inpainting(image, regions)
            
            results[engine] = result.success
            print(f"  ✓ {engine} 引擎测试{'成功' if result.success else '失败'}")
            
        except Exception as e:
            results[engine] = False
            print(f"  ✗ {engine} 引擎测试失败: {str(e)}")
    
    return results


def test_factory_integration():
    """测试工厂模式集成"""
    print("\n=== 测试工厂模式集成 ===")
    
    try:
        # 测试工厂可用性
        available_engines = InpaintModelFactory.get_supported_engines()
        print(f"支持的引擎: {available_engines}")
        
        # 测试模型可用性
        availability = InpaintModelFactory.list_available_models()
        print(f"引擎可用性: {availability}")
        
        return True
        
    except Exception as e:
        print(f"✗ 工厂模式测试失败: {str(e)}")
        return False


def test_config_management():
    """测试配置管理"""
    print("\n=== 测试配置管理 ===")

    try:
        config_manager = get_config_manager()

        # 测试获取inpaint配置
        inpaint_config = config_manager.get_inpaint_config()
        print(f"当前inpaint配置: {inpaint_config}")

        # 测试更新配置
        config_manager.update_inpaint_config('opencv', device='cpu', quality='low')
        updated_config = config_manager.get_inpaint_config()
        print(f"更新后配置: {updated_config}")

        return True

    except Exception as e:
        print(f"✗ 配置管理测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """运行所有测试"""
    print("开始测试重构后的InpaintProcessor功能\n")
    
    tests = [
        test_inpaint_processor_initialization,
        test_mask_creation,
        test_inpainting_process,
        test_different_engines,
        test_factory_integration,
        test_config_management
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"测试异常: {str(e)}")
    
    print(f"\n=== 测试总结 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过！")
        return 0
    else:
        print("❌ 部分测试失败")
        return 1


if __name__ == "__main__":
    sys.exit(main())
