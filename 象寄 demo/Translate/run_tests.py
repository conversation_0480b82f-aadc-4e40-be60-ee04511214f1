#!/usr/bin/env python3
"""
测试运行脚本
运行所有模块的单元测试
"""
import unittest
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)


def run_all_tests():
    """运行所有测试"""
    print("="*60)
    print("运行翻译系统模块化架构测试")
    print("="*60)
    
    # 发现并运行所有测试
    loader = unittest.TestLoader()
    start_dir = os.path.join(project_root, 'tests')
    suite = loader.discover(start_dir, pattern='test_*.py')
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2, buffer=True)
    result = runner.run(suite)
    
    # 输出测试结果摘要
    print("\n" + "="*60)
    print("测试结果摘要")
    print("="*60)
    print(f"运行测试数量: {result.testsRun}")
    print(f"失败数量: {len(result.failures)}")
    print(f"错误数量: {len(result.errors)}")
    print(f"跳过数量: {len(result.skipped)}")
    
    if result.failures:
        print(f"\n失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}")
    
    if result.errors:
        print(f"\n错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}")
    
    # 计算成功率
    if result.testsRun > 0:
        success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100
        print(f"\n测试成功率: {success_rate:.1f}%")
    
    print("="*60)
    
    # 返回是否所有测试都通过
    return len(result.failures) == 0 and len(result.errors) == 0


def run_specific_test(test_module):
    """运行特定模块的测试"""
    print(f"运行 {test_module} 模块测试...")
    
    try:
        # 导入测试模块
        module = __import__(f'tests.{test_module}', fromlist=[test_module])
        
        # 创建测试套件
        loader = unittest.TestLoader()
        suite = loader.loadTestsFromModule(module)
        
        # 运行测试
        runner = unittest.TextTestRunner(verbosity=2)
        result = runner.run(suite)
        
        return len(result.failures) == 0 and len(result.errors) == 0
        
    except ImportError as e:
        print(f"无法导入测试模块 {test_module}: {e}")
        return False


def main():
    """主函数"""
    if len(sys.argv) > 1:
        # 运行特定测试
        test_module = sys.argv[1]
        if test_module.startswith('test_'):
            test_module = test_module[5:]  # 移除 'test_' 前缀
        
        success = run_specific_test(f'test_{test_module}')
        sys.exit(0 if success else 1)
    else:
        # 运行所有测试
        success = run_all_tests()
        sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
