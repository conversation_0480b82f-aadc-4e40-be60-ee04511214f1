# Processors 模块使用文档

本文档详细介绍了翻译项目中各个处理器模块的功能、使用方法和API接口。

## 目录

1. [OCR处理器 (OCRProcessor)](#ocr处理器-ocrprocessor)
2. [布局处理器 (LayoutProcessor)](#布局处理器-layoutprocessor)
3. [字体处理器 (FontProcessor)](#字体处理器-fontprocessor)
4. [翻译处理器 (TranslationProcessor)](#翻译处理器-translationprocessor)
5. [图像修复处理器 (InpaintProcessor)](#图像修复处理器-inpaintprocessor)
6. [渲染器 (Renderer)](#渲染器-renderer)

---

## OCR处理器 (OCRProcessor)

### 功能概述
OCR处理器负责图像中的文字检测与识别，包括中文文本识别、样式分析、颜色提取和高度测量等功能。

### 主要特性
- **智能文字检测**：基于PaddleOCR的高精度文字识别
- **样式分析**：提取字体大小、颜色、粗体等样式信息
- **像素级高度统一**：自动统一相似高度的文字区域
- **调试支持**：生成详细的调试图像和数据

### 核心方法

#### `process_image(image_path, confidence_threshold=0.5)`
处理图像进行OCR识别

**参数：**
- `image_path` (str): 图像文件路径
- `confidence_threshold` (float): 置信度阈值，默认0.5

**返回：**
- `ProcessingResult`: 包含OCRResult的处理结果

**示例：**
```python
from processors.ocr_processor import OCRProcessor

# 初始化处理器
ocr_processor = OCRProcessor()

# 处理图像
result = ocr_processor.process_image("example.jpg", confidence_threshold=0.7)

if result.success:
    ocr_result = result.data
    print(f"检测到 {ocr_result.chinese_count} 个中文区域")
    
    # 访问中文区域
    for region in ocr_result.chinese_regions:
        print(f"文字: {region.text}")
        print(f"位置: {region.bbox}")
        print(f"样式: {region.style_info}")
```

#### `extract_complete_style(image, region_bbox)`
提取文字区域的完整样式信息

**参数：**
- `image` (np.ndarray): 原始图像
- `region_bbox` (tuple): 区域边界框 (x, y, w, h)

**返回：**
- `dict`: 包含字体大小、颜色、粗体等样式信息

#### `measure_roi_text_height(roi_bgr, debug_prefix=None)`
测量文字区域的精确高度

**参数：**
- `roi_bgr` (np.ndarray): 文字区域图像
- `debug_prefix` (str, optional): 调试文件前缀

**返回：**
- `int`: 文字实际高度（像素）

### 调试功能
启用OCR调试后，会生成以下文件：
- `chinese_regions.png`: 中文区域标注图像
- `final_regions.png`: 最终处理区域图像
- `ocr_data.json`: 完整的OCR数据
- `height_measurement/`: 高度测量调试图像

---

## 布局处理器 (LayoutProcessor)

### 功能概述
布局处理器负责分析文字区域的空间关系，识别对齐模式和分布规律，为后续的翻译和渲染提供布局约束。

### 主要特性
- **智能对齐检测**：识别左对齐、居中对齐、右对齐组
- **分布组识别**：检测同行内的文字分布关系
- **间距分析**：分析文字间距的规律性
- **布局模式识别**：识别网格、列表、自由等布局模式

### 核心方法

#### `process_layout(chinese_regions)`
分析文字区域的布局关系

**参数：**
- `chinese_regions` (List[TextRegion]): 中文文字区域列表

**返回：**
- `ProcessingResult`: 包含LayoutResult的处理结果

**示例：**
```python
from processors.layout_processor import LayoutProcessor

# 初始化处理器
layout_processor = LayoutProcessor()

# 分析布局
result = layout_processor.process_layout(chinese_regions)

if result.success:
    layout_result = result.data
    
    # 访问对齐组
    alignment = layout_result.horizontal_alignment
    print(f"左对齐组: {len(alignment.get('left_groups', []))}")
    print(f"分布组: {len(alignment.get('distribution_groups', []))}")
    
    # 访问布局模式
    print(f"布局模式: {layout_result.layout_mode}")
```

### 布局结果结构
```python
LayoutResult:
    layout_mode: str              # 布局模式 (grid/list/free)
    regions: List[dict]           # 区域数据
    horizontal_alignment: dict    # 水平对齐信息
    vertical_distribution: dict   # 垂直分布信息
    alignment_strategies: dict    # 对齐策略
```

### 调试功能
启用布局调试后，会生成：
- `alignment_groups.png`: 对齐组可视化
- `distribution_grid.png`: 分布网格分析
- `layout_data.json`: 完整的布局数据

---

## 字体处理器 (FontProcessor)

### 功能概述
字体处理器负责识别和匹配文字区域的字体，确保翻译后的文字与原文字体风格一致。

### 主要特性
- **字体相似度匹配**：基于SSIM算法的字体匹配
- **日文支持检测**：自动检测字体是否支持日文字符
- **兜底字体机制**：不支持日文时自动使用思源黑体
- **字体缓存优化**：提高字体匹配效率

### 核心方法

#### `process_regions(image, regions)`
处理文字区域进行字体匹配

**参数：**
- `image` (np.ndarray): 原始图像
- `regions` (List[TextRegion]): 文字区域列表

**返回：**
- `ProcessingResult`: 包含FontMatchResult列表的处理结果

**示例：**
```python
from processors.font_processor import FontProcessor

# 初始化处理器
font_processor = FontProcessor()

# 字体匹配
result = font_processor.process_regions(image, chinese_regions)

if result.success:
    font_results = result.data
    
    for font_result in font_results:
        print(f"文字: {font_result.text}")
        print(f"匹配字体: {font_result.matched_font}")
        print(f"相似度: {font_result.confidence:.3f}")
        print(f"日文支持: {font_result.supports_japanese}")
```

#### `test_japanese_support(font_path)`
测试字体是否支持日文字符

**参数：**
- `font_path` (str): 字体文件路径

**返回：**
- `bool`: 是否支持日文

### FontMatchResult结构
```python
FontMatchResult:
    text: str                    # 原始文字
    matched_font: str           # 匹配的字体名称
    font_path: str              # 字体文件路径
    confidence: float           # 匹配相似度
    supports_japanese: bool     # 是否支持日文
    region_id: int             # 对应的区域ID
```

---

## 翻译处理器 (TranslationProcessor)

### 功能概述
翻译处理器负责文本翻译和布局感知的分组约束，确保翻译后的文字符合原始布局的视觉效果。

### 主要特性
- **智能文本翻译**：基于字典的中日文翻译
- **布局感知分组**：根据布局分析结果进行分组
- **统一约束应用**：同组内字体大小一致性
- **10%容忍度机制**：边界溢出的智能处理

### 核心方法

#### `process_translation(image, regions, font_results, layout_result)`
处理翻译任务

**参数：**
- `image` (np.ndarray): 原始图像
- `regions` (List[TextRegion]): 文字区域列表
- `font_results` (List[FontMatchResult]): 字体匹配结果
- `layout_result` (LayoutResult): 布局分析结果

**返回：**
- `ProcessingResult`: 包含TranslationResult列表的处理结果

**示例：**
```python
from processors.translation_processor import TranslationProcessor

# 初始化处理器
translation_processor = TranslationProcessor(weight_adjustment=400)

# 处理翻译
result = translation_processor.process_translation(
    image, chinese_regions, font_results, layout_result
)

if result.success:
    translation_results = result.data
    
    for trans_result in translation_results:
        print(f"原文: {trans_result.original_text}")
        print(f"译文: {trans_result.translated_text}")
        print(f"位置: ({trans_result.render_x}, {trans_result.render_y})")
        print(f"字号: {trans_result.font_size}")
```

### TranslationResult结构
```python
TranslationResult:
    original_text: str          # 原始文字
    translated_text: str        # 翻译文字
    bbox: tuple                 # 原始边界框
    render_x: int              # 渲染X坐标
    render_y: int              # 渲染Y坐标
    font_size: int             # 字体大小
    font_info: FontMatchResult # 字体信息
    style_info: StyleInfo      # 样式信息
    group_info: dict           # 分组信息
```

---

## 图像修复处理器 (InpaintProcessor)

### 功能概述
图像修复处理器负责去除原始图像中的中文文字，为后续的译文渲染提供干净的背景。

### 主要特性
- **智能文字掩码生成**：精确标记需要去除的文字区域
- **多种修复算法**：支持TELEA和NS修复算法
- **边缘优化**：自动扩展掩码边缘以获得更好效果
- **调试支持**：保存掩码图像用于调试

### 核心方法

#### `process_inpainting(image, chinese_regions, save_debug=True)`
处理图像修复，去除中文文字

**参数：**
- `image` (np.ndarray): 原始图像
- `chinese_regions` (List[TextRegion]): 中文文字区域列表
- `save_debug` (bool): 是否保存调试图像

**返回：**
- `ProcessingResult`: 包含修复后图像的处理结果

**示例：**
```python
from processors.inpaint_processor import InpaintProcessor

# 初始化处理器
inpaint_processor = InpaintProcessor()

# 图像修复
result = inpaint_processor.process_inpainting(
    image, chinese_regions, save_debug=True
)

if result.success:
    inpainted_image = result.data
    print("图像修复完成")
```

#### `inpaint_with_different_methods(image, mask, method="telea")`
使用不同方法进行图像修复

**参数：**
- `image` (np.ndarray): 原始图像
- `mask` (np.ndarray): 掩码
- `method` (str): 修复方法 ("telea", "ns")

**返回：**
- `np.ndarray`: 修复后的图像

---

## 渲染器 (Renderer)

### 功能概述
渲染器负责将翻译后的文字绘制到修复后的图像上，生成最终的翻译结果图像。

### 主要特性
- **精确位置计算**：基于布局分析的智能位置计算
- **字体大小优化**：自动调整字体大小以适应原始区域
- **可变字体支持**：支持字体粗细调节
- **渲染日志记录**：详细记录每个文字的渲染信息

### 核心方法

#### `render_translations(base_image, translation_results, layout_result)`
渲染翻译文字到图像上

**参数：**
- `base_image` (np.ndarray): 基础图像（已去除原文字）
- `translation_results` (List[TranslationResult]): 翻译结果列表
- `layout_result` (LayoutResult): 布局分析结果

**返回：**
- `ProcessingResult`: 包含最终图像和渲染日志的结果

**示例：**
```python
from processors.renderer import Renderer

# 初始化渲染器
renderer = Renderer(weight_adjustment=400)

# 渲染翻译
result = renderer.render_translations(
    inpainted_image, translation_results, layout_result
)

if result.success:
    result_data = result.data
    final_image = result_data['image']
    render_log = result_data['render_log']
    
    print(f"渲染完成，共渲染 {len(render_log)} 个文字")
```

### 渲染配置
渲染器会自动计算每个文字的渲染配置：
- 文字位置 (text_x, text_y)
- 字体大小 (font_size)
- 字体路径 (font_path)
- 文字颜色 (text_color)

---

## 使用流程示例

以下是完整的处理流程示例：

```python
from processors.ocr_processor import OCRProcessor
from processors.layout_processor import LayoutProcessor
from processors.font_processor import FontProcessor
from processors.translation_processor import TranslationProcessor
from processors.inpaint_processor import InpaintProcessor
from processors.renderer import Renderer

# 1. OCR识别
ocr_processor = OCRProcessor()
ocr_result = ocr_processor.process_image("input.jpg").data

# 2. 布局分析
layout_processor = LayoutProcessor()
layout_result = layout_processor.process_layout(ocr_result.chinese_regions).data

# 3. 字体匹配
font_processor = FontProcessor()
font_results = font_processor.process_regions(image, ocr_result.chinese_regions).data

# 4. 翻译处理
translation_processor = TranslationProcessor()
translation_results = translation_processor.process_translation(
    image, ocr_result.chinese_regions, font_results, layout_result
).data

# 5. 图像修复
inpaint_processor = InpaintProcessor()
inpainted_image = inpaint_processor.process_inpainting(
    image, ocr_result.chinese_regions
).data

# 6. 渲染译文
renderer = Renderer()
final_result = renderer.render_translations(
    inpainted_image, translation_results, layout_result
).data

final_image = final_result['image']
```

## 配置和调试

### 调试功能启用
在配置文件中启用调试功能：
```python
config.enable_ocr_debug = True
config.enable_layout_debug = True
```

### 输出目录结构
```
debug_images/
├── ocr_processor/
│   ├── chinese_regions.png
│   ├── final_regions.png
│   ├── ocr_data.json
│   └── height_measurement/
└── layout_processor/
    ├── alignment_groups.png
    ├── distribution_grid.png
    └── layout_data.json
```

## 错误处理

所有处理器都返回`ProcessingResult`对象，包含：
- `success` (bool): 处理是否成功
- `data`: 处理结果数据
- `error_message` (str): 错误信息（如果失败）

```python
result = processor.process_something(...)
if result.success:
    data = result.data
    # 处理成功的逻辑
else:
    print(f"处理失败: {result.error_message}")
```

## 性能优化建议

1. **字体缓存**：字体处理器和翻译处理器都使用缓存机制
2. **批量处理**：尽量批量处理多个区域
3. **调试开关**：生产环境关闭调试功能
4. **内存管理**：及时释放大图像对象

---

本文档涵盖了所有processors模块的核心功能和使用方法。如需更详细的API文档，请参考各模块的源代码注释。
