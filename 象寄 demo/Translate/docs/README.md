# Processors 模块文档

欢迎使用翻译项目的Processors模块！本目录包含了所有处理器模块的详细文档。

## 📚 文档目录

### 🚀 [快速入门指南](processors_quick_start.md)
- **适合人群**: 新用户、快速上手
- **内容**: 5分钟快速开始、基础示例、常见问题
- **推荐**: ⭐⭐⭐⭐⭐ 必读文档

### 📖 [详细使用文档](processors_usage.md)
- **适合人群**: 深入使用、功能探索
- **内容**: 完整功能介绍、使用流程、调试方法
- **推荐**: ⭐⭐⭐⭐ 深入了解

### 🔧 [API参考文档](processors_api_reference.md)
- **适合人群**: 开发者、集成使用
- **内容**: 详细API接口、参数说明、异常处理
- **推荐**: ⭐⭐⭐ 开发必备

## 🏗️ 模块架构

```
processors/
├── ocr_processor.py          # OCR文字识别
├── layout_processor.py       # 布局分析
├── font_processor.py         # 字体匹配
├── translation_processor.py  # 翻译处理
├── inpaint_processor.py      # 图像修复
└── renderer.py              # 文字渲染
```

## 🔄 处理流程

```mermaid
graph TD
    A[输入图像] --> B[OCR识别]
    B --> C[布局分析]
    B --> D[字体匹配]
    C --> E[翻译处理]
    D --> E
    B --> F[图像修复]
    E --> G[文字渲染]
    F --> G
    G --> H[输出图像]
```

## 📋 模块功能概览

| 模块 | 主要功能 | 输入 | 输出 |
|------|----------|------|------|
| **OCRProcessor** | 文字识别、样式分析 | 图像文件 | 文字区域、样式信息 |
| **LayoutProcessor** | 布局分析、对齐检测 | 文字区域 | 布局关系、对齐组 |
| **FontProcessor** | 字体匹配、日文支持检测 | 图像、文字区域 | 字体匹配结果 |
| **TranslationProcessor** | 文本翻译、约束应用 | 多种输入 | 翻译结果 |
| **InpaintProcessor** | 图像修复、文字去除 | 图像、文字区域 | 修复后图像 |
| **Renderer** | 文字渲染、图像合成 | 多种输入 | 最终图像 |

## 🚀 快速开始

### 1. 环境准备
```bash
pip install paddleocr opencv-python pillow numpy scikit-image fonttools
```

### 2. 基础使用
```python
from processors.ocr_processor import OCRProcessor

# 初始化并使用
ocr = OCRProcessor()
result = ocr.process_image("your_image.jpg")

if result.success:
    print(f"检测到 {result.data.chinese_count} 个中文区域")
```

### 3. 完整流程
```python
# 导入所有处理器
from processors import *

# 按顺序处理
ocr_result = OCRProcessor().process_image("image.jpg")
layout_result = LayoutProcessor().process_layout(ocr_result.data.chinese_regions)
font_results = FontProcessor().process_regions(image, ocr_result.data.chinese_regions)
# ... 继续其他步骤
```

## 🎯 核心特性

### OCR处理器
- ✅ 高精度文字识别（基于PaddleOCR）
- ✅ 智能样式分析（颜色、字体、粗体）
- ✅ 像素级高度统一
- ✅ 完整调试支持

### 布局处理器
- ✅ 智能对齐检测（左、中、右对齐）
- ✅ 分布组识别（同行文字关系）
- ✅ 间距模式分析
- ✅ 布局模式识别（网格、列表、自由）

### 字体处理器
- ✅ 基于SSIM的字体匹配
- ✅ 日文字符支持检测
- ✅ 自动兜底字体机制
- ✅ 字体缓存优化

### 翻译处理器
- ✅ 智能文本翻译
- ✅ 布局感知分组
- ✅ 统一约束应用
- ✅ 10%容忍度机制

### 图像修复处理器
- ✅ 精确文字掩码生成
- ✅ 多种修复算法（TELEA、NS）
- ✅ 边缘优化处理
- ✅ 调试图像保存

### 渲染器
- ✅ 精确位置计算
- ✅ 字体大小自动优化
- ✅ 可变字体支持
- ✅ 详细渲染日志

## 🔧 配置选项

### 调试模式
```python
from config.settings import get_config_manager

config = get_config_manager()
config.config.enable_ocr_debug = True      # OCR调试
config.config.enable_layout_debug = True   # 布局调试
```

### 参数调整
```python
# OCR置信度
ocr.process_image("image.jpg", confidence_threshold=0.7)

# 字体粗细
TranslationProcessor(weight_adjustment=600)
Renderer(weight_adjustment=600)

# 修复算法
inpaint.inpaint_with_different_methods(image, mask, method="ns")
```

## 📊 调试功能

### OCR调试输出
```
debug_images/ocr_processor/
├── chinese_regions.png      # 中文区域标注
├── final_regions.png        # 最终处理区域
├── ocr_data.json           # 完整OCR数据
└── height_measurement/     # 高度测量调试
```

### 布局调试输出
```
debug_images/layout_processor/
├── alignment_groups.png     # 对齐组可视化
├── distribution_grid.png    # 分布网格分析
└── layout_data.json        # 完整布局数据
```

## 🎨 颜色编码系统

调试图像中的颜色含义：
- 🔵 **蓝色**: 中文区域
- 🔴 **红色**: 非中文区域
- 🟢 **绿色**: 左对齐组 / 最终处理区域
- 🔵 **蓝色**: 居中对齐组
- 🔴 **红色**: 右对齐组
- 🟣 **紫色**: 分布组
- 🟠 **橙色**: 未分组区域
- 🟡 **黄色**: 对齐基准线

## ⚡ 性能优化

### 最佳实践
1. **复用实例**: 避免重复创建处理器
2. **批量处理**: 一次处理多个区域
3. **缓存利用**: 字体大小计算会自动缓存
4. **内存管理**: 及时释放大图像对象

### 性能监控
```python
import time

start_time = time.time()
result = processor.process_something(...)
end_time = time.time()

print(f"处理耗时: {end_time - start_time:.2f}秒")
```

## 🐛 故障排除

### 常见问题

#### OCR识别不准确
- 调整置信度阈值
- 检查图像质量和分辨率
- 启用调试模式查看识别结果

#### 字体匹配效果差
- 检查字体文件是否存在
- 确认字体支持日文字符
- 查看字体映射配置

#### 布局分析错误
- 启用布局调试查看分析过程
- 检查文字区域的位置信息
- 调整对齐阈值参数

#### 渲染位置不对
- 检查翻译结果的位置计算
- 确认布局分析结果正确
- 调整字体大小和粗细参数

### 错误处理
```python
try:
    result = processor.process_something(...)
    if result.success:
        # 处理成功
        data = result.data
    else:
        # 处理失败
        print(f"错误: {result.error_message}")
except Exception as e:
    print(f"异常: {e}")
```

## 📈 版本历史

### v1.0.0 (当前版本)
- ✅ 完整的模块化架构
- ✅ 六大核心处理器
- ✅ 完善的调试功能
- ✅ 详细的文档支持

## 🤝 贡献指南

### 代码风格
- 遵循PEP 8规范
- 使用类型注解
- 添加详细的文档字符串
- 保持方法简洁（单一职责）

### 测试要求
- 为新功能编写单元测试
- 确保所有测试通过
- 测试覆盖率 > 80%

### 文档更新
- 更新相关的使用文档
- 添加API参考信息
- 提供使用示例

## 📞 支持与反馈

### 获取帮助
1. 查看文档（本目录下的所有.md文件）
2. 查看源代码注释
3. 运行调试模式查看详细信息
4. 检查错误日志和异常信息

### 反馈渠道
- 代码问题：查看源代码和注释
- 使用问题：参考文档和示例
- 功能建议：考虑模块化扩展

## 📄 许可证

本项目采用开源许可证，详情请查看项目根目录的LICENSE文件。

---

**开始您的翻译之旅！** 🚀

推荐阅读顺序：
1. [快速入门指南](processors_quick_start.md) - 快速上手
2. [详细使用文档](processors_usage.md) - 深入了解
3. [API参考文档](processors_api_reference.md) - 开发参考

祝您使用愉快！ 🎉
