# Processors 快速入门指南

本指南帮助您快速上手使用翻译项目的各个处理器模块。

## 环境准备

### 依赖安装
```bash
pip install paddleocr opencv-python pillow numpy scikit-image fonttools
```

### 项目结构
```
processors/
├── __init__.py
├── ocr_processor.py          # OCR文字识别
├── layout_processor.py       # 布局分析
├── font_processor.py         # 字体匹配
├── translation_processor.py  # 翻译处理
├── inpaint_processor.py      # 图像修复
└── renderer.py              # 文字渲染
```

## 5分钟快速开始

### 1. 基础导入
```python
import cv2
import numpy as np
from processors.ocr_processor import OCRProcessor
from processors.layout_processor import LayoutProcessor
from processors.font_processor import FontProcessor
from processors.translation_processor import TranslationProcessor
from processors.inpaint_processor import InpaintProcessor
from processors.renderer import Renderer
```

### 2. 简单使用示例
```python
# 加载图像
image = cv2.imread("your_image.jpg")

# 1. OCR识别
ocr_processor = OCRProcessor()
ocr_result = ocr_processor.process_image("your_image.jpg")

if ocr_result.success:
    chinese_regions = ocr_result.data.chinese_regions
    print(f"检测到 {len(chinese_regions)} 个中文区域")
    
    # 2. 布局分析
    layout_processor = LayoutProcessor()
    layout_result = layout_processor.process_layout(chinese_regions)
    
    # 3. 字体匹配
    font_processor = FontProcessor()
    font_results = font_processor.process_regions(image, chinese_regions)
    
    # 4. 翻译处理
    translation_processor = TranslationProcessor()
    translation_results = translation_processor.process_translation(
        image, chinese_regions, font_results.data, layout_result.data
    )
    
    # 5. 图像修复
    inpaint_processor = InpaintProcessor()
    inpainted_image = inpaint_processor.process_inpainting(image, chinese_regions)
    
    # 6. 渲染译文
    renderer = Renderer()
    final_result = renderer.render_translations(
        inpainted_image.data, translation_results.data, layout_result.data
    )
    
    # 保存结果
    cv2.imwrite("translated_image.jpg", final_result.data['image'])
    print("翻译完成！")
```

## 单独使用各个处理器

### OCR处理器
```python
from processors.ocr_processor import OCRProcessor

# 初始化
ocr = OCRProcessor()

# 处理图像
result = ocr.process_image("image.jpg", confidence_threshold=0.7)

if result.success:
    ocr_data = result.data
    
    # 访问中文区域
    for region in ocr_data.chinese_regions:
        print(f"文字: {region.text}")
        print(f"位置: {region.bbox}")
        print(f"置信度: {region.score}")
        
        # 访问样式信息
        if region.style_info:
            print(f"字体大小: {region.style_info['estimated_font_size']}")
            print(f"文字颜色: {region.style_info['text_color']}")
```

### 布局处理器
```python
from processors.layout_processor import LayoutProcessor

# 初始化
layout = LayoutProcessor()

# 分析布局（需要先有OCR结果）
result = layout.process_layout(chinese_regions)

if result.success:
    layout_data = result.data
    
    # 查看对齐组
    alignment = layout_data.horizontal_alignment
    print(f"左对齐组: {len(alignment.get('left_groups', []))}")
    print(f"分布组: {len(alignment.get('distribution_groups', []))}")
    
    # 查看布局模式
    print(f"布局模式: {layout_data.layout_mode}")
```

### 字体处理器
```python
from processors.font_processor import FontProcessor

# 初始化
font = FontProcessor()

# 字体匹配
result = font.process_regions(image, chinese_regions)

if result.success:
    font_results = result.data
    
    for font_result in font_results:
        print(f"文字: {font_result.text}")
        print(f"匹配字体: {font_result.matched_font}")
        print(f"相似度: {font_result.confidence:.3f}")
        print(f"支持日文: {font_result.supports_japanese}")
```

### 翻译处理器
```python
from processors.translation_processor import TranslationProcessor

# 初始化
translator = TranslationProcessor(weight_adjustment=400)

# 翻译处理
result = translator.process_translation(
    image, chinese_regions, font_results, layout_result
)

if result.success:
    translations = result.data
    
    for trans in translations:
        print(f"原文: {trans.original_text}")
        print(f"译文: {trans.translated_text}")
        print(f"位置: ({trans.render_x}, {trans.render_y})")
        print(f"字号: {trans.font_size}")
```

### 图像修复处理器
```python
from processors.inpaint_processor import InpaintProcessor

# 初始化
inpaint = InpaintProcessor()

# 图像修复
result = inpaint.process_inpainting(image, chinese_regions, save_debug=True)

if result.success:
    inpainted_image = result.data
    cv2.imwrite("inpainted.jpg", inpainted_image)
    print("图像修复完成")
```

### 渲染器
```python
from processors.renderer import Renderer

# 初始化
renderer = Renderer(weight_adjustment=400)

# 渲染译文
result = renderer.render_translations(
    inpainted_image, translation_results, layout_result
)

if result.success:
    final_data = result.data
    final_image = final_data['image']
    render_log = final_data['render_log']
    
    cv2.imwrite("final.jpg", final_image)
    print(f"渲染完成，共渲染 {len(render_log)} 个文字")
```

## 配置和调试

### 启用调试模式
```python
from config.settings import get_config_manager

config_manager = get_config_manager()
config_manager.config.enable_ocr_debug = True
config_manager.config.enable_layout_debug = True
```

### 自定义配置
```python
# 调整OCR置信度
ocr_result = ocr.process_image("image.jpg", confidence_threshold=0.8)

# 调整字体粗细
translator = TranslationProcessor(weight_adjustment=600)
renderer = Renderer(weight_adjustment=600)

# 选择修复算法
inpainted = inpaint.inpaint_with_different_methods(image, mask, method="ns")
```

## 错误处理最佳实践

### 统一错误处理
```python
def safe_process(processor_func, *args, **kwargs):
    """安全处理函数"""
    try:
        result = processor_func(*args, **kwargs)
        if result.success:
            return result.data
        else:
            print(f"处理失败: {result.error_message}")
            return None
    except Exception as e:
        print(f"异常: {e}")
        return None

# 使用示例
ocr_data = safe_process(ocr.process_image, "image.jpg")
if ocr_data:
    # 继续处理
    pass
```

### 链式处理
```python
def process_image_pipeline(image_path):
    """完整的图像处理流水线"""
    
    # 加载图像
    image = cv2.imread(image_path)
    if image is None:
        return None, "无法加载图像"
    
    # OCR识别
    ocr = OCRProcessor()
    ocr_result = ocr.process_image(image_path)
    if not ocr_result.success:
        return None, f"OCR失败: {ocr_result.error_message}"
    
    chinese_regions = ocr_result.data.chinese_regions
    if not chinese_regions:
        return None, "未检测到中文文字"
    
    # 布局分析
    layout = LayoutProcessor()
    layout_result = layout.process_layout(chinese_regions)
    if not layout_result.success:
        return None, f"布局分析失败: {layout_result.error_message}"
    
    # 字体匹配
    font = FontProcessor()
    font_result = font.process_regions(image, chinese_regions)
    if not font_result.success:
        return None, f"字体匹配失败: {font_result.error_message}"
    
    # 翻译处理
    translator = TranslationProcessor()
    trans_result = translator.process_translation(
        image, chinese_regions, font_result.data, layout_result.data
    )
    if not trans_result.success:
        return None, f"翻译失败: {trans_result.error_message}"
    
    # 图像修复
    inpaint = InpaintProcessor()
    inpaint_result = inpaint.process_inpainting(image, chinese_regions)
    if not inpaint_result.success:
        return None, f"图像修复失败: {inpaint_result.error_message}"
    
    # 渲染译文
    renderer = Renderer()
    render_result = renderer.render_translations(
        inpaint_result.data, trans_result.data, layout_result.data
    )
    if not render_result.success:
        return None, f"渲染失败: {render_result.error_message}"
    
    return render_result.data['image'], "处理成功"

# 使用示例
final_image, message = process_image_pipeline("input.jpg")
if final_image is not None:
    cv2.imwrite("output.jpg", final_image)
    print(message)
else:
    print(f"处理失败: {message}")
```

## 性能优化技巧

### 1. 复用处理器实例
```python
# 好的做法：复用实例
ocr = OCRProcessor()
layout = LayoutProcessor()
font = FontProcessor()

for image_path in image_list:
    ocr_result = ocr.process_image(image_path)
    # ... 其他处理
```

### 2. 批量处理
```python
# 批量处理多个区域
font_results = font.process_regions(image, all_regions)
```

### 3. 缓存利用
```python
# 翻译处理器和渲染器会自动缓存字体大小计算结果
# 相同的文字和字体组合会直接使用缓存
```

### 4. 内存管理
```python
# 及时释放大图像
del large_image

# 使用适当的图像尺寸
if image.shape[0] > 2000 or image.shape[1] > 2000:
    # 考虑缩放图像
    scale = min(2000/image.shape[0], 2000/image.shape[1])
    new_size = (int(image.shape[1]*scale), int(image.shape[0]*scale))
    image = cv2.resize(image, new_size)
```

## 常见问题解决

### Q: OCR识别不准确怎么办？
```python
# 1. 调整置信度阈值
result = ocr.process_image("image.jpg", confidence_threshold=0.3)

# 2. 检查图像质量
# 确保图像清晰，文字大小适中

# 3. 启用调试模式查看识别结果
config.enable_ocr_debug = True
```

### Q: 字体匹配效果不好？
```python
# 1. 检查字体文件是否存在
font_processor = FontProcessor()
print(f"可用字体: {list(font_processor.font_mapping.keys())}")

# 2. 确保字体支持日文
supports_jp = font_processor.test_japanese_support(font_path)
```

### Q: 翻译结果位置不对？
```python
# 1. 启用布局调试
config.enable_layout_debug = True

# 2. 检查布局分析结果
layout_result = layout.process_layout(chinese_regions)
print(f"布局模式: {layout_result.data.layout_mode}")
```

### Q: 渲染文字太大或太小？
```python
# 1. 调整字体粗细
renderer = Renderer(weight_adjustment=300)  # 更细
renderer = Renderer(weight_adjustment=700)  # 更粗

# 2. 检查样式信息
for region in chinese_regions:
    if region.style_info:
        print(f"原始高度: {region.style_info['precise_height']}")
```

## 下一步

- 查看 [详细使用文档](processors_usage.md) 了解更多功能
- 查看 [API参考文档](processors_api_reference.md) 了解完整接口
- 查看源代码了解实现细节
- 参考 `main.py` 和 `pipeline.py` 了解完整的使用流程

---

本快速入门指南涵盖了processors模块的基本使用方法。通过这些示例，您应该能够快速开始使用各个处理器模块进行图像翻译任务。
