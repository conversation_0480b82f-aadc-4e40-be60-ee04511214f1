"""
图像修复模型工厂
参考 translation_models 的设计模式
"""
from typing import Dict, Any, List
from .base import BaseInpaintModel
from .lama_model import LamaInpaintModel
from .opencv_model import OpenCVInpaintModel


class InpaintModelFactory:
    """图像修复模型工厂"""
    
    _models = {}
    _supported_engines = {
        'lama': LamaInpaintModel,
        'opencv': OpenCVInpaintModel,
        # 未来扩展
        # 'sd': StableDiffusionInpaintModel,
    }
    
    @classmethod
    def create_model(cls, engine: str, config: Dict[str, Any]) -> BaseInpaintModel:
        """
        创建图像修复模型实例
        
        Args:
            engine: 修复引擎名称 ('lama', 'opencv', 'sd')
            config: 模型配置
            
        Returns:
            BaseInpaintModel: 修复模型实例
            
        Raises:
            ValueError: 不支持的修复引擎
        """
        if engine not in cls._supported_engines:
            raise ValueError(f"不支持的修复引擎: {engine}. 支持的引擎: {list(cls._supported_engines.keys())}")
        
        # 使用单例模式，避免重复初始化
        model_key = f"{engine}_{hash(str(sorted(config.items())))}"
        
        if model_key not in cls._models:
            model_class = cls._supported_engines[engine]
            cls._models[model_key] = model_class(config)
            print(f"创建图像修复模型: {engine} ({config.get('device', 'cpu')})")
        
        return cls._models[model_key]
    
    @classmethod
    def create_with_fallback(cls, preferred_engine: str, config: Dict[str, Any]) -> BaseInpaintModel:
        """
        创建模型并提供回退机制
        
        Args:
            preferred_engine: 首选修复引擎
            config: 模型配置
            
        Returns:
            BaseInpaintModel: 修复模型实例（可能是回退模型）
        """
        try:
            # 尝试创建首选模型
            model = cls.create_model(preferred_engine, config)
            
            # 尝试初始化模型
            if model.initialize_model():
                print(f"成功初始化 {preferred_engine} 修复引擎")
                return model
            else:
                print(f"{preferred_engine} 初始化失败，尝试回退")
                
        except Exception as e:
            print(f"创建 {preferred_engine} 失败: {str(e)}")
        
        # 回退到OpenCV
        print("回退到OpenCV修复引擎")
        fallback_config = {
            'device': 'cpu',
            'method': 'telea',
            'quality': config.get('quality', 'medium')
        }
        
        opencv_model = cls.create_model('opencv', fallback_config)
        opencv_model.initialize_model()
        return opencv_model
    
    @classmethod
    def get_supported_engines(cls) -> List[str]:
        """获取支持的修复引擎列表"""
        return list(cls._supported_engines.keys())
    
    @classmethod
    def get_engine_info(cls, engine: str) -> Dict[str, Any]:
        """
        获取修复引擎信息
        
        Args:
            engine: 引擎名称
            
        Returns:
            Dict[str, Any]: 引擎信息
        """
        engine_info = {
            'lama': {
                'name': 'LaMa (Large Mask Inpainting)',
                'description': '专为大区域修复优化的深度学习模型',
                'quality': 'high',
                'speed': 'medium',
                'requirements': ['torch', 'torchvision'],
                'supports_devices': ['cpu', 'mps', 'cuda'],
                'best_for': '大面积文字擦除、复杂背景修复'
            },
            'opencv': {
                'name': 'OpenCV Inpainting',
                'description': '基于传统图像处理的修复算法',
                'quality': 'medium',
                'speed': 'fast',
                'requirements': ['opencv-python'],
                'supports_devices': ['cpu'],
                'best_for': '小面积修复、简单背景、快速处理'
            }
        }
        
        return engine_info.get(engine, {})
    
    @classmethod
    def clear_cache(cls):
        """清空模型缓存"""
        cls._models.clear()
        print("图像修复模型缓存已清空")
    
    @classmethod
    def list_available_models(cls) -> Dict[str, bool]:
        """
        列出可用的修复模型
        
        Returns:
            Dict[str, bool]: 引擎名称 -> 是否可用
        """
        availability = {}
        
        for engine in cls._supported_engines:
            try:
                # 尝试创建一个测试配置
                test_config = {'device': 'cpu', 'quality': 'low'}
                model = cls.create_model(engine, test_config)
                availability[engine] = model.initialize_model()
            except Exception:
                availability[engine] = False
        
        return availability 