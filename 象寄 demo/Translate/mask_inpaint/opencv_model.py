"""
OpenCV 图像修复实现
基于传统的TELEA和NS算法
"""
import time
import cv2
import numpy as np
from typing import Dict, Any

from .base import BaseInpaintModel, InpaintResult


class OpenCVInpaintModel(BaseInpaintModel):
    """OpenCV 图像修复模型"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化OpenCV修复模型
        
        Args:
            config: 配置字典，支持的参数：
                - method: 修复方法 ('telea' 或 'ns')
                - radius: 修复半径 (3-10)
                - quality: 质量等级 ('low', 'medium', 'high')
        """
        super().__init__(config)
        
        self.method = config.get('method', 'telea')
        self.radius = config.get('radius', 5)
        self.model_name = f"opencv_{self.method}"
        
        # 根据质量等级调整参数
        if self.quality == 'low':
            self.radius = 3
        elif self.quality == 'medium':
            self.radius = 5
        elif self.quality == 'high':
            self.radius = 7
            
        print(f"OpenCV修复模型初始化: 方法={self.method}, 半径={self.radius}")
    
    def initialize_model(self) -> bool:
        """
        初始化模型（OpenCV无需特殊初始化）
        
        Returns:
            bool: 始终返回True
        """
        self.model_initialized = True
        return True
    
    def inpaint(self, image: np.ndarray, mask: np.ndarray) -> InpaintResult:
        """
        使用OpenCV进行图像修复
        
        Args:
            image: 原始图像 (H, W, 3) BGR格式
            mask: 掩码图像 (H, W) 二值图像
            
        Returns:
            InpaintResult: 修复结果
        """
        start_time = time.time()
        
        try:
            # 验证输入
            if not self._validate_inputs(image, mask):
                return InpaintResult(
                    success=False,
                    inpainted_image=image.copy(),
                    processing_time=0,
                    error_message="输入参数无效",
                    engine_name=self.model_name
                )
            
            # 选择修复算法
            if self.method == 'telea':
                inpaint_flag = cv2.INPAINT_TELEA
            elif self.method == 'ns':
                inpaint_flag = cv2.INPAINT_NS
            else:
                # 默认使用TELEA
                inpaint_flag = cv2.INPAINT_TELEA
            
            # 执行修复
            inpainted_image = cv2.inpaint(image, mask, self.radius, inpaint_flag)
            
            processing_time = time.time() - start_time
            
            return InpaintResult(
                success=True,
                inpainted_image=inpainted_image,
                processing_time=processing_time,
                confidence=0.7,  # OpenCV方法置信度中等
                engine_name=self.model_name
            )
            
        except Exception as e:
            processing_time = time.time() - start_time
            return InpaintResult(
                success=False,
                inpainted_image=image.copy(),
                processing_time=processing_time,
                error_message=f"OpenCV修复失败: {str(e)}",
                engine_name=self.model_name
            )
    
    def inpaint_with_different_methods(
        self, 
        image: np.ndarray, 
        mask: np.ndarray, 
        method: str = "telea"
    ) -> InpaintResult:
        """
        使用指定方法进行图像修复（兼容原有接口）
        
        Args:
            image: 原始图像
            mask: 掩码
            method: 修复方法 ("telea", "ns")
            
        Returns:
            InpaintResult: 修复结果
        """
        # 临时更改方法
        original_method = self.method
        self.method = method
        
        # 执行修复
        result = self.inpaint(image, mask)
        
        # 恢复原方法
        self.method = original_method
        
        return result
    
    def get_inpaint_radius_recommendation(self, mask: np.ndarray) -> int:
        """
        根据掩码特征推荐修复半径（兼容原有接口）
        
        Args:
            mask: 掩码图像
            
        Returns:
            int: 推荐的修复半径
        """
        try:
            # 计算掩码中连通区域的平均大小
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if not contours:
                return 3  # 默认值
            
            # 计算平均轮廓面积
            areas = [cv2.contourArea(contour) for contour in contours]
            avg_area = sum(areas) / len(areas)
            
            # 基于面积推荐半径
            if avg_area < 100:
                return 3
            elif avg_area < 500:
                return 5
            elif avg_area < 1000:
                return 7
            else:
                return 10
                
        except Exception:
            return 5  # 出错时返回默认值
    
    def apply_morphological_operations(self, mask: np.ndarray, operation: str = "close") -> np.ndarray:
        """
        对掩码应用形态学操作（兼容原有接口）
        
        Args:
            mask: 输入掩码
            operation: 操作类型 ("close", "open", "dilate", "erode")
            
        Returns:
            np.ndarray: 处理后的掩码
        """
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        
        if operation == "close":
            return cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
        elif operation == "open":
            return cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
        elif operation == "dilate":
            return cv2.dilate(mask, kernel, iterations=1)
        elif operation == "erode":
            return cv2.erode(mask, kernel, iterations=1)
        else:
            return mask 